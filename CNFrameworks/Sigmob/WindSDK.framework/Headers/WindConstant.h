//
//  WindConstant.h
//  WindSDK
//
//  Created by Codi on 2021/10/29.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface WindConstant : NSObject

extern NSString * const SIGMOB_SDK_VER;

extern NSString * const kWindSplashExtraAdSize;
extern NSString * const kWindSplashExtraRootViewController;


extern NSString * const SIGMOB_NOTIFICATION_APPSTORE;
extern NSString * const SIGMOB_VIDEO_CONTROLL;
extern NSString * const SIGMOB_VISIBLE_CHANGED;



extern NSString * const SM_AD_SOURCE_CHANNEL_SIGMOB;
extern NSString * const WindAdSceneDesc;
extern NSString * const WindAdSceneId;
extern NSString* const kWindNativeAdKeyVideoDuration;

@end

NS_ASSUME_NONNULL_END
