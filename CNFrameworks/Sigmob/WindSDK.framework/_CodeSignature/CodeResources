<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/WindAdOptions.h</key>
		<data>
		7+stynMjYAfMnqC9RH3D0yHnSQE=
		</data>
		<key>Headers/WindAdRequest.h</key>
		<data>
		nLMFJD+SfqjMN0hUdeBqo+1xiYg=
		</data>
		<key>Headers/WindAds.h</key>
		<data>
		4MNdkLIOgyyyZjzCcXwQ0/zBTeY=
		</data>
		<key>Headers/WindBiddingProtocol.h</key>
		<data>
		amz5jsjO3irR5uO8BHSMIv9A0Ns=
		</data>
		<key>Headers/WindConstant.h</key>
		<data>
		OO6eJ0xUeCuo3kYORgSW4QydbLw=
		</data>
		<key>Headers/WindDislikeWords.h</key>
		<data>
		PIab0wxXx5jLZTcX6lhlCD2mFg8=
		</data>
		<key>Headers/WindEnum.h</key>
		<data>
		VPayfIl2K8iv96repNXpE+f/0aA=
		</data>
		<key>Headers/WindLocation.h</key>
		<data>
		iyaf1ZCZZkLlH9e41f+oFBzBsqk=
		</data>
		<key>Headers/WindMediaView.h</key>
		<data>
		WXRnVZi1przUsBaQnE3Z+oEdB2o=
		</data>
		<key>Headers/WindNativeAd.h</key>
		<data>
		WHyW9ehY57PCBHdvCLbrIpAAiRg=
		</data>
		<key>Headers/WindNativeAdView.h</key>
		<data>
		A+bvFF9p9egb9m943bG5xgscnEE=
		</data>
		<key>Headers/WindNativeAdsManager.h</key>
		<data>
		GwsLMzOo8h81HS25YDxqIrg1XMY=
		</data>
		<key>Headers/WindNewIntersititialAd.h</key>
		<data>
		tqzPc3QpDJV36ODyy0LAzFG5E8U=
		</data>
		<key>Headers/WindPrivacyProtocol.h</key>
		<data>
		JT6YLiZrZuPcNQ+P6am/Ec87P3U=
		</data>
		<key>Headers/WindRewardInfo.h</key>
		<data>
		9rWwiy+/sMZZ/vVa6BMRnQ58QBE=
		</data>
		<key>Headers/WindRewardVideoAd.h</key>
		<data>
		AZ93B+i0EutHJzxIQ+Th6PakGVA=
		</data>
		<key>Headers/WindSDK.h</key>
		<data>
		oet4JoQ4TyLhOHPQPSnXL9ulUIk=
		</data>
		<key>Headers/WindSplashAdView.h</key>
		<data>
		Cvs17YtOetrWGIx1Fkc7Lib6gHU=
		</data>
		<key>Info.plist</key>
		<data>
		5MSQ2zcBC5305+7vlrQSecbH+C4=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		0ZvmBKevp0o7w/q+Oi1VVj9GGw0=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		/Ob96ulOlVP7Gztxfa9S4AcuRpg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/WindAdOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			7+stynMjYAfMnqC9RH3D0yHnSQE=
			</data>
			<key>hash2</key>
			<data>
			nPqisTPZsjDTjuqNVzVbpMltHgjexaEa4fq/L/ngfKA=
			</data>
		</dict>
		<key>Headers/WindAdRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			nLMFJD+SfqjMN0hUdeBqo+1xiYg=
			</data>
			<key>hash2</key>
			<data>
			F3uDWGd6P6WFHhE7yquCil+QNrgdyJIc+EpTnjYtfYY=
			</data>
		</dict>
		<key>Headers/WindAds.h</key>
		<dict>
			<key>hash</key>
			<data>
			4MNdkLIOgyyyZjzCcXwQ0/zBTeY=
			</data>
			<key>hash2</key>
			<data>
			J49NSAI1qQeqi/G+a1CcejD9DgIVUCD1KqiHiKPX3cg=
			</data>
		</dict>
		<key>Headers/WindBiddingProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			amz5jsjO3irR5uO8BHSMIv9A0Ns=
			</data>
			<key>hash2</key>
			<data>
			r5jE/UifLj9OPB5SwPX/Xy9KqbJ/4kJ3gwlFDy/cOi0=
			</data>
		</dict>
		<key>Headers/WindConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			OO6eJ0xUeCuo3kYORgSW4QydbLw=
			</data>
			<key>hash2</key>
			<data>
			4OzF4aWq8awks1t1Ln+iT3D+zp6GWWAnP9Q28+ff2Kk=
			</data>
		</dict>
		<key>Headers/WindDislikeWords.h</key>
		<dict>
			<key>hash</key>
			<data>
			PIab0wxXx5jLZTcX6lhlCD2mFg8=
			</data>
			<key>hash2</key>
			<data>
			D1x/tXfF1vinJE6ZfWdM88wpVVAu1LvMIGC8tOFq+nY=
			</data>
		</dict>
		<key>Headers/WindEnum.h</key>
		<dict>
			<key>hash</key>
			<data>
			VPayfIl2K8iv96repNXpE+f/0aA=
			</data>
			<key>hash2</key>
			<data>
			MZweM0RF2mPL+ES+5BvBHvgkHTTKZ6S7/162iz9MYco=
			</data>
		</dict>
		<key>Headers/WindLocation.h</key>
		<dict>
			<key>hash</key>
			<data>
			iyaf1ZCZZkLlH9e41f+oFBzBsqk=
			</data>
			<key>hash2</key>
			<data>
			UadeJkeIRFWteR24GV0KyJKPypAb5PSB7E34cr+g1pU=
			</data>
		</dict>
		<key>Headers/WindMediaView.h</key>
		<dict>
			<key>hash</key>
			<data>
			WXRnVZi1przUsBaQnE3Z+oEdB2o=
			</data>
			<key>hash2</key>
			<data>
			1BZ173iImhR8H2MBmSdlpWrVLu3zFxY/nf0XIQVkZj8=
			</data>
		</dict>
		<key>Headers/WindNativeAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			WHyW9ehY57PCBHdvCLbrIpAAiRg=
			</data>
			<key>hash2</key>
			<data>
			5arQVHAdAMayf2sRE8bTYmkS9wPWeMALBdQcBLJfEMw=
			</data>
		</dict>
		<key>Headers/WindNativeAdView.h</key>
		<dict>
			<key>hash</key>
			<data>
			A+bvFF9p9egb9m943bG5xgscnEE=
			</data>
			<key>hash2</key>
			<data>
			xVLZzVzCoSiKYnVTodwTopPbjbuslAoc+2E/dq6ZbJk=
			</data>
		</dict>
		<key>Headers/WindNativeAdsManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			GwsLMzOo8h81HS25YDxqIrg1XMY=
			</data>
			<key>hash2</key>
			<data>
			MzBQJ0XqfIceMzmt7u8EsnCJbpXYYTxr+qBqgHchv00=
			</data>
		</dict>
		<key>Headers/WindNewIntersititialAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			tqzPc3QpDJV36ODyy0LAzFG5E8U=
			</data>
			<key>hash2</key>
			<data>
			gH6W0GMEMATPW9mbVfNb7Vw5oLB1A5UqMqB3t2mpfa4=
			</data>
		</dict>
		<key>Headers/WindPrivacyProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			JT6YLiZrZuPcNQ+P6am/Ec87P3U=
			</data>
			<key>hash2</key>
			<data>
			m7w9IHFg4Wh42siFsj45aT81WHR0mpL+zpyaQR6Uzj8=
			</data>
		</dict>
		<key>Headers/WindRewardInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			9rWwiy+/sMZZ/vVa6BMRnQ58QBE=
			</data>
			<key>hash2</key>
			<data>
			JHXHBLd1kvnU4mh0qqSoQdNp1Y9pVlh9nC6RG9eNhug=
			</data>
		</dict>
		<key>Headers/WindRewardVideoAd.h</key>
		<dict>
			<key>hash</key>
			<data>
			AZ93B+i0EutHJzxIQ+Th6PakGVA=
			</data>
			<key>hash2</key>
			<data>
			buqQUFmjtCnDVml+diw8e0s+d/PDfO24G5DD2vhoIoM=
			</data>
		</dict>
		<key>Headers/WindSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			oet4JoQ4TyLhOHPQPSnXL9ulUIk=
			</data>
			<key>hash2</key>
			<data>
			eQ7zwM1FP8N5sj2hO28DES6Q35pU2R/SapoJc7HdztE=
			</data>
		</dict>
		<key>Headers/WindSplashAdView.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cvs17YtOetrWGIx1Fkc7Lib6gHU=
			</data>
			<key>hash2</key>
			<data>
			v4nTA8yHLmyhctbIOFE/DuAJywEyhi4OKhlUSj5SsrM=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			0ZvmBKevp0o7w/q+Oi1VVj9GGw0=
			</data>
			<key>hash2</key>
			<data>
			/jjudwA5g4DvqEOzRwW3y5bqAZqkW2wU5GOgqflsz+0=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			/Ob96ulOlVP7Gztxfa9S4AcuRpg=
			</data>
			<key>hash2</key>
			<data>
			oPtiJ2YUNtx2Me7YEA5viC6ldsJ8TnjzTR6T7WjZ5mc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
