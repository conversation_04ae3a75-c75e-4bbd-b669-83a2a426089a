// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sigmob_adslot.proto

// This CPP symbol can be defined to use imports that match up to the framework
// imports needed when using CocoaPods.
#if !defined(WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS)
 #define WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS 0
#endif

#if WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS
 #import <WindFoundation/WindGPBProtocolBuffers.h>
#else
 #import "WindGPBProtocolBuffers.h"
#endif

#if GOOGLE_PROTOBUF_OBJC_VERSION < 30004
#error This file was generated by a newer version of protoc which is incompatible with your Protocol Buffer library sources.
#endif
#if 30004 < GOOGLE_PROTOBUF_OBJC_MIN_SUPPORTED_VERSION
#error This file was generated by an older version of protoc which is incompatible with your Protocol Buffer library sources.
#endif

// @@protoc_insertion_point(imports)

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"

CF_EXTERN_C_BEGIN

@class SigmobSize;
@class SigmobVideo;
@class WindSDKAdCache;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - WindSDKSigmobAdslotRoot

/**
 * Exposes the extension registry for this file.
 *
 * The base class provides:
 * @code
 *   + (WindGPBExtensionRegistry *)extensionRegistry;
 * @endcode
 * which is a @c WindGPBExtensionRegistry that includes all the extensions defined by
 * this file and all files that it depends on.
 **/
WindGPB_FINAL @interface WindSDKSigmobAdslotRoot : WindGPBRootObject
@end

#pragma mark - WindSDKAdSlot

typedef WindGPB_ENUM(WindSDKAdSlot_FieldNumber) {
  WindSDKAdSlot_FieldNumber_AdslotId = 1,
  WindSDKAdSlot_FieldNumber_AdslotSize = 2,
  WindSDKAdSlot_FieldNumber_AdslotTypeArray = 3,
  WindSDKAdSlot_FieldNumber_Bidfloor = 4,
  WindSDKAdSlot_FieldNumber_Vid = 5,
  WindSDKAdSlot_FieldNumber_LatestCampId = 6,
  WindSDKAdSlot_FieldNumber_LatestCrid = 7,
  WindSDKAdSlot_FieldNumber_MaterialTypeArray = 8,
  WindSDKAdSlot_FieldNumber_SdkStrategyIndex = 9,
  WindSDKAdSlot_FieldNumber_ApiStrategyIndex = 10,
  WindSDKAdSlot_FieldNumber_CreativeTypeArray = 12,
  WindSDKAdSlot_FieldNumber_AlgorithmFloor = 13,
  WindSDKAdSlot_FieldNumber_Ext = 14,
  WindSDKAdSlot_FieldNumber_AdCaches = 15,
  WindSDKAdSlot_FieldNumber_Video = 16,
  WindSDKAdSlot_FieldNumber_AdCount = 28,
  WindSDKAdSlot_FieldNumber_SupportTemplateIdArray = 46,
  WindSDKAdSlot_FieldNumber_MediaRequestCount = 52,
  WindSDKAdSlot_FieldNumber_MediaReadyCount = 53,
  WindSDKAdSlot_FieldNumber_PreReqTime = 54,
  WindSDKAdSlot_FieldNumber_ReqIntervalTime = 55,
  WindSDKAdSlot_FieldNumber_CachedAdSize = 56,
};

/**
 * 广告位信息
 **/
WindGPB_FINAL @interface WindSDKAdSlot : WindGPBMessage

/** 必填！广告位ID */
@property(nonatomic, readwrite, copy, null_resettable) NSString *adslotId;

/** 必填！广告位尺寸。暂不填写，后续产品确定策略有再议 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobSize *adslotSize;
/** Test to see if @c adslotSize has been set. */
@property(nonatomic, readwrite) BOOL hasAdslotSize;

/** 选填！支持的广告位类型（目前只支持一种）。 1=通用奖励视频 2=开屏 */
@property(nonatomic, readwrite, strong, null_resettable) WindGPBUInt32Array *adslotTypeArray;
/** The number of items in @c adslotTypeArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger adslotTypeArray_Count;

/** 底价 */
@property(nonatomic, readwrite) uint32_t bidfloor;

/** 曝光ID，该广告位此次请求曝光唯一ID。可用requestid + adslot序列号生成 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *vid;

/** 上一次播放的campaign id */
@property(nonatomic, readwrite, copy, null_resettable) NSString *latestCampId;

/** 上一次播放的Creative id */
@property(nonatomic, readwrite, copy, null_resettable) NSString *latestCrid;

/** 媒体支持的素材格式列表。激励视频广告先填空。（素材的子类型，比如开屏的图片类型） */
@property(nonatomic, readwrite, strong, null_resettable) WindGPBUInt32Array *materialTypeArray;
/** The number of items in @c materialTypeArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger materialTypeArray_Count;

/** sdk聚合策略位置。从1开始 */
@property(nonatomic, readwrite) uint32_t sdkStrategyIndex;

/** api聚合策略位置。从1开始 */
@property(nonatomic, readwrite) uint32_t apiStrategyIndex;

/** 媒体支持的创意类型，与reponse中MaterialMeta的creative_type一致（sdk从2.10开始补传开屏支持的创意类型3和8） */
@property(nonatomic, readwrite, strong, null_resettable) WindGPBUInt32Array *creativeTypeArray;
/** The number of items in @c creativeTypeArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger creativeTypeArray_Count;

/** 期望价格，对应【算法 eCPM Floor】 */
@property(nonatomic, readwrite) uint32_t algorithmFloor;

/** key:template_type,表示模版类型，0表示没有伴随条；key:animate_type,表示动画类型，0表示没有伴随条 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableDictionary<NSString*, NSString*> *ext;
/** The number of items in @c ext without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger ext_Count;

/** key:广告位id */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableDictionary<NSString*, WindSDKAdCache*> *adCaches;
/** The number of items in @c adCaches without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger adCaches_Count;

/** 视频素材请求参数 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVideo *video;
/** Test to see if @c video has been set. */
@property(nonatomic, readwrite) BOOL hasVideo;

/** 请求的广告数 */
@property(nonatomic, readwrite) uint32_t adCount;

/** sdk支持的原生模版ID列表 */
@property(nonatomic, readwrite, strong, null_resettable) WindGPBUInt32Array *supportTemplateIdArray;
/** The number of items in @c supportTemplateIdArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger supportTemplateIdArray_Count;

/** 上一轮开发者请求量 */
@property(nonatomic, readwrite) uint32_t mediaRequestCount;

/** 上一轮给开发者的填充量 */
@property(nonatomic, readwrite) uint32_t mediaReadyCount;

/** 上一轮请求时间(unix时间戳，毫秒) */
@property(nonatomic, readwrite) int64_t preReqTime;

/** 配置间隔时间 */
@property(nonatomic, readwrite) int32_t reqIntervalTime;

/** 缓存的广告数量 */
@property(nonatomic, readwrite) uint32_t cachedAdSize;

@end

#pragma mark - WindSDKAdCache

typedef WindGPB_ENUM(WindSDKAdCache_FieldNumber) {
  WindSDKAdCache_FieldNumber_AdType = 1,
  WindSDKAdCache_FieldNumber_CridsArray = 2,
};

WindGPB_FINAL @interface WindSDKAdCache : WindGPBMessage

/** 广告类型 */
@property(nonatomic, readwrite) int32_t adType;

/** 缓存的创意列表 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<NSString*> *cridsArray;
/** The number of items in @c cridsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger cridsArray_Count;

@end

NS_ASSUME_NONNULL_END

CF_EXTERN_C_END

#pragma clang diagnostic pop

// @@protoc_insertion_point(global_scope)
