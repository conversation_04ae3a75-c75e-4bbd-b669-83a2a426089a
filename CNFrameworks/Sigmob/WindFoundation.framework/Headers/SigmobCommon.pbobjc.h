// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sigmob_common.proto

// This CPP symbol can be defined to use imports that match up to the framework
// imports needed when using CocoaPods.
#if !defined(WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS)
 #define WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS 0
#endif

#if WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS
 #import <WindFoundation/WindGPBProtocolBuffers.h>
#else
 #import "WindGPBProtocolBuffers.h"
#endif

#if GOOGLE_PROTOBUF_OBJC_VERSION < 30004
#error This file was generated by a newer version of protoc which is incompatible with your Protocol Buffer library sources.
#endif
#if 30004 < GOOGLE_PROTOBUF_OBJC_MIN_SUPPORTED_VERSION
#error This file was generated by an older version of protoc which is incompatible with your Protocol Buffer library sources.
#endif

// @@protoc_insertion_point(imports)

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"

CF_EXTERN_C_BEGIN

@class SigmobDeviceId;
@class SigmobGeo;
@class SigmobPermission;
@class SigmobSize;
@class SigmobVersion;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - SigmobCommonRoot

/**
 * Exposes the extension registry for this file.
 *
 * The base class provides:
 * @code
 *   + (WindGPBExtensionRegistry *)extensionRegistry;
 * @endcode
 * which is a @c WindGPBExtensionRegistry that includes all the extensions defined by
 * this file and all files that it depends on.
 **/
WindGPB_FINAL @interface SigmobCommonRoot : WindGPBRootObject
@end

#pragma mark - SigmobVersion

typedef WindGPB_ENUM(SigmobVersion_FieldNumber) {
  SigmobVersion_FieldNumber_Major = 1,
  SigmobVersion_FieldNumber_Minor = 2,
  SigmobVersion_FieldNumber_Micro = 3,
  SigmobVersion_FieldNumber_VersionStr = 4,
};

WindGPB_FINAL @interface SigmobVersion : WindGPBMessage

/** 主版本号, 必填！ */
@property(nonatomic, readwrite) uint32_t major;

/** 副版本号, 必填！ */
@property(nonatomic, readwrite) uint32_t minor;

/** 子版本号, 必填！ */
@property(nonatomic, readwrite) uint32_t micro;

/** 字符串表示的版本号,在客户端无法解析出三段式数字版本号信息时，用此字段表示 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *versionStr;

@end

#pragma mark - SigmobApp

typedef WindGPB_ENUM(SigmobApp_FieldNumber) {
  SigmobApp_FieldNumber_AppSid = 1,
  SigmobApp_FieldNumber_AppVersion = 2,
  SigmobApp_FieldNumber_AppPackage = 3,
  SigmobApp_FieldNumber_Orientation = 4,
  SigmobApp_FieldNumber_Name = 5,
  SigmobApp_FieldNumber_Idfv = 6,
  SigmobApp_FieldNumber_ChannelId = 7,
  SigmobApp_FieldNumber_ProductId = 8,
  SigmobApp_FieldNumber_SupportHTTP = 9,
  SigmobApp_FieldNumber_AdNetworkIdsArray = 10,
  SigmobApp_FieldNumber_SupportSkVersion = 11,
  SigmobApp_FieldNumber_SdkExtCapArray = 12,
  SigmobApp_FieldNumber_InstallTime = 15,
  SigmobApp_FieldNumber_Mraid1Version = 17,
  SigmobApp_FieldNumber_Mraid2Version = 18,
  SigmobApp_FieldNumber_Permission = 19,
};

WindGPB_FINAL @interface SigmobApp : WindGPBMessage

/** 应用ID */
@property(nonatomic, readwrite, copy, null_resettable) NSString *appSid;

/** 必填！应用版本 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVersion *appVersion;
/** Test to see if @c appVersion has been set. */
@property(nonatomic, readwrite) BOOL hasAppVersion;

/** 必填！应用包名。IOS设备为bundle id */
@property(nonatomic, readwrite, copy, null_resettable) NSString *appPackage;

/** app方向：0: MaskAll、1:portrait、2:landspace */
@property(nonatomic, readwrite) uint32_t orientation;

/** app名称 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *name;

/** Vindor标示符，适用于对内：例如分析用户在应用内的行为等。 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *idfv;

/** 应用商店的渠道标识。字典同yomob的渠道字典，当前也只有yomob需要 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *channelId;

/** 媒体在投放系统中的apple id（iOS） 或 package name（Android） */
@property(nonatomic, readwrite, copy, null_resettable) NSString *productId;

/** app是否支持http */
@property(nonatomic, readwrite) BOOL supportHTTP;

/** 媒体在info.plist中配置的SKAdNetworkId列表 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<NSString*> *adNetworkIdsArray;
/** The number of items in @c adNetworkIdsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger adNetworkIdsArray_Count;

/** 如果为空，则不支持sk，填写当前支持的最大版本目前取值：1.0/2.0 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *supportSkVersion;

/** 当前SDK版本所支持的扩展的功能1: overlay(仅iOS)；2:storekit(仅iOS);3:支持客户端bidding */
@property(nonatomic, readwrite, strong, null_resettable) WindGPBUInt32Array *sdkExtCapArray;
/** The number of items in @c sdkExtCapArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger sdkExtCapArray_Count;

/** 媒体应用安装时间，单位为秒 */
@property(nonatomic, readwrite) int64_t installTime;

/** 必填！mraid1.x协议版本号 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVersion *mraid1Version;
/** Test to see if @c mraid1Version has been set. */
@property(nonatomic, readwrite) BOOL hasMraid1Version;

/** 必填！mraid2.x协议版本号 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVersion *mraid2Version;
/** Test to see if @c mraid2Version has been set. */
@property(nonatomic, readwrite) BOOL hasMraid2Version;

/** 上报app配置的权限信息 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobPermission *permission;
/** Test to see if @c permission has been set. */
@property(nonatomic, readwrite) BOOL hasPermission;

@end

#pragma mark - SigmobPermission

typedef WindGPB_ENUM(SigmobPermission_FieldNumber) {
  SigmobPermission_FieldNumber_Accelerometer = 1,
};

WindGPB_FINAL @interface SigmobPermission : WindGPBMessage

/** 加速度传感器, 影响摇一摇, true:可以使用互动能力，false:不可以使用互动能力 */
@property(nonatomic, readwrite) BOOL accelerometer;

@end

#pragma mark - SigmobGeo

typedef WindGPB_ENUM(SigmobGeo_FieldNumber) {
  SigmobGeo_FieldNumber_Lat = 1,
  SigmobGeo_FieldNumber_Lon = 2,
  SigmobGeo_FieldNumber_Language = 3,
  SigmobGeo_FieldNumber_TimeZone = 4,
  SigmobGeo_FieldNumber_CityCode = 5,
  SigmobGeo_FieldNumber_Country = 6,
  SigmobGeo_FieldNumber_RegionCode = 7,
  SigmobGeo_FieldNumber_SecondsFromGmt = 8,
  SigmobGeo_FieldNumber_Accuracy = 9,
};

WindGPB_FINAL @interface SigmobGeo : WindGPBMessage

/** 纬度 */
@property(nonatomic, readwrite) float lat;

/** 经度 */
@property(nonatomic, readwrite) float lon;

/** 语言（大写） */
@property(nonatomic, readwrite, copy, null_resettable) NSString *language;

/** 时区 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *timeZone;

/** 地域编码 */
@property(nonatomic, readwrite) uint64_t cityCode;

/** 国家 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *country;

/** ip库中识别出来的编码，可能是市级编码、省级编码 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *regionCode;

/** 当前时区距离隔离的秒数 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *secondsFromGmt;

/** 经纬度半径，单位：米 */
@property(nonatomic, readwrite) double accuracy;

@end

#pragma mark - SigmobDevice

typedef WindGPB_ENUM(SigmobDevice_FieldNumber) {
  SigmobDevice_FieldNumber_DeviceType = 1,
  SigmobDevice_FieldNumber_OsType = 2,
  SigmobDevice_FieldNumber_OsVersion = 3,
  SigmobDevice_FieldNumber_Vendor = 4,
  SigmobDevice_FieldNumber_Model = 5,
  SigmobDevice_FieldNumber_Did = 6,
  SigmobDevice_FieldNumber_ScreenSize = 7,
  SigmobDevice_FieldNumber_Geo = 8,
  SigmobDevice_FieldNumber_Dpi = 9,
  SigmobDevice_FieldNumber_IsRoot = 10,
  SigmobDevice_FieldNumber_DiskSize = 11,
  SigmobDevice_FieldNumber_BatteryState = 13,
  SigmobDevice_FieldNumber_BatteryLevel = 14,
  SigmobDevice_FieldNumber_BatterySaveEnabled = 15,
  SigmobDevice_FieldNumber_DeviceName = 16,
  SigmobDevice_FieldNumber_StartTimestamp = 17,
  SigmobDevice_FieldNumber_AndroidApiLevel = 18,
  SigmobDevice_FieldNumber_MemSize = 19,
  SigmobDevice_FieldNumber_TotalDiskSize = 20,
  SigmobDevice_FieldNumber_FreeDiskSize = 21,
  SigmobDevice_FieldNumber_SdTotalDiskSize = 22,
  SigmobDevice_FieldNumber_SdFreeDiskSize = 23,
  SigmobDevice_FieldNumber_Resolution = 24,
  SigmobDevice_FieldNumber_SystemUpdateTime = 25,
  SigmobDevice_FieldNumber_InternalName = 26,
  SigmobDevice_FieldNumber_BootMark = 27,
  SigmobDevice_FieldNumber_UpdateMark = 28,
  SigmobDevice_FieldNumber_FbTime = 29,
  SigmobDevice_FieldNumber_FileMountId = 30,
};

WindGPB_FINAL @interface SigmobDevice : WindGPBMessage

/** 设备类型。0:unknown、1:iPhone、2:iPad、3:iPod、4:Android phone、5:Android pad */
@property(nonatomic, readwrite) uint32_t deviceType;

/** 操作系统类型. 1=IOS；2=Android */
@property(nonatomic, readwrite) uint32_t osType;

/** 必填！操作系统版本 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVersion *osVersion;
/** Test to see if @c osVersion has been set. */
@property(nonatomic, readwrite) BOOL hasOsVersion;

/** 必填！设备厂商名称，中文需要UTF-8编码 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *vendor;

/** 必填！设备型号，中文需要UTF-8编码 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *model;

/** 必填！唯一设备标识，必需按要求填写 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobDeviceId *did;
/** Test to see if @c did has been set. */
@property(nonatomic, readwrite) BOOL hasDid;

/** 必填！设备屏幕宽高 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobSize *screenSize;
/** Test to see if @c screenSize has been set. */
@property(nonatomic, readwrite) BOOL hasScreenSize;

/** 地理信息 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobGeo *geo;
/** Test to see if @c geo has been set. */
@property(nonatomic, readwrite) BOOL hasGeo;

/** 屏幕密度 */
@property(nonatomic, readwrite) uint32_t dpi;

/** 是否越狱（true：越狱） */
@property(nonatomic, readwrite) BOOL isRoot;

/** 磁盘大小（单位Byte）【已废弃】 */
@property(nonatomic, readwrite) uint64_t diskSize;

/** 电池充电的状态（0=UnKnow、1=Unplugged、2=Charging、3=Full） */
@property(nonatomic, readwrite) uint32_t batteryState;

/** 电池电量百分比 */
@property(nonatomic, readwrite) float batteryLevel;

/** 是否开启低电量模式 */
@property(nonatomic, readwrite) BOOL batterySaveEnabled;

/** 设备名称 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *deviceName;

/** 设备启动时间,unix时间戳 (10 位，从1970开始的时间戳) */
@property(nonatomic, readwrite) int64_t startTimestamp;

/** Android API level */
@property(nonatomic, readwrite) uint32_t androidApiLevel;

/** 系统内存大小，安卓必填（单位Byte） */
@property(nonatomic, readwrite) uint64_t memSize;

/** 手机磁盘总大小（单位Byte） */
@property(nonatomic, readwrite) uint64_t totalDiskSize;

/** 手机磁盘剩余大小（单位Byte） */
@property(nonatomic, readwrite) uint64_t freeDiskSize;

/** 设备SD磁盘总大小（单位Byte） */
@property(nonatomic, readwrite) uint64_t sdTotalDiskSize;

/** 设备SD磁盘剩余大小（单位Byte） */
@property(nonatomic, readwrite) uint64_t sdFreeDiskSize;

/** 设备分辨率（单位px） */
@property(nonatomic, readwrite, strong, null_resettable) SigmobSize *resolution;
/** Test to see if @c resolution has been set. */
@property(nonatomic, readwrite) BOOL hasResolution;

/** 系统更新的时间 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *systemUpdateTime;

/** 手机mode编码 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *internalName;

/** 手机重启时间 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *bootMark;

/** 手机系统更新时间 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *updateMark;

/** 仅ios填充。设备初始化时间 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *fbTime;

/** ios caid生成参数 原mnt_id */
@property(nonatomic, readwrite, copy, null_resettable) NSString *fileMountId;

@end

#pragma mark - SigmobDeviceId

typedef WindGPB_ENUM(SigmobDeviceId_FieldNumber) {
  SigmobDeviceId_FieldNumber_Idfa = 1,
  SigmobDeviceId_FieldNumber_Udid = 2,
  SigmobDeviceId_FieldNumber_UserId = 3,
  SigmobDeviceId_FieldNumber_Imei = 4,
  SigmobDeviceId_FieldNumber_AndroidId = 5,
  SigmobDeviceId_FieldNumber_AndroidUuid = 6,
  SigmobDeviceId_FieldNumber_Imsi = 7,
  SigmobDeviceId_FieldNumber_Gaid = 8,
  SigmobDeviceId_FieldNumber_Uid = 9,
  SigmobDeviceId_FieldNumber_Brand = 10,
  SigmobDeviceId_FieldNumber_Imei1 = 11,
  SigmobDeviceId_FieldNumber_Imei2 = 12,
  SigmobDeviceId_FieldNumber_Oaid = 13,
  SigmobDeviceId_FieldNumber_Vaid = 14,
  SigmobDeviceId_FieldNumber_Aaid = 15,
  SigmobDeviceId_FieldNumber_MsaUdid = 16,
  SigmobDeviceId_FieldNumber_IdfaMd5 = 17,
  SigmobDeviceId_FieldNumber_ImeiMd5 = 18,
  SigmobDeviceId_FieldNumber_AndroidIdMd5 = 19,
  SigmobDeviceId_FieldNumber_GaidMd5 = 20,
  SigmobDeviceId_FieldNumber_OaidMd5 = 21,
  SigmobDeviceId_FieldNumber_Imei1Md5 = 22,
  SigmobDeviceId_FieldNumber_Imei2Md5 = 23,
  SigmobDeviceId_FieldNumber_IsCustomIdfa = 26,
  SigmobDeviceId_FieldNumber_IsCustomLocation = 35,
  SigmobDeviceId_FieldNumber_IsCustomIdfv = 36,
};

/**
 * 唯一用户标识，优先使用明文，必需按要求填写，具体填写指导详见接口说明文档
 **/
WindGPB_FINAL @interface SigmobDeviceId : WindGPBMessage

/** [必填]iOS设备的IDFA，格式要求[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12} */
@property(nonatomic, readwrite, copy, null_resettable) NSString *idfa;

/** 内部生成 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *udid;

/** 用户id */
@property(nonatomic, readwrite, copy, null_resettable) NSString *userId;

/** [必填]设备IMEI，IOS设备填空 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *imei;

/** [必填]android设备的android_id，ios设备填空 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *androidId;

/** Android系统为开发者提供的用于标识手机设备的串号,会根据不同的手机设备返回IMEI，MEID或者ESN码 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *androidUuid;

/** Sim Serial Number */
@property(nonatomic, readwrite, copy, null_resettable) NSString *imsi;

/** 在Android设备上找到Google advertising ID (GAID) */
@property(nonatomic, readwrite, copy, null_resettable) NSString *gaid;

/** 用户在sigmob的唯一标识。 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *uid;

/** 手机品牌名称，安卓专属 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *brand;

@property(nonatomic, readwrite, copy, null_resettable) NSString *imei1;

@property(nonatomic, readwrite, copy, null_resettable) NSString *imei2;

/** msa 联盟 oaid 匿名设备标识符(Open Anonymous Device Identifier)最长64位 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *oaid;

/** msa 联盟 vaid 开发者匿名设备标识符(Vender Anonymous Device Identifier)最长64位 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *vaid;

/** msa 联盟 aaid 应用匿名设备标识符(Application Anonymous Device Identifier)最长64位 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *aaid;

/** msa 联盟 udid */
@property(nonatomic, readwrite, copy, null_resettable) NSString *msaUdid;

/** iOS设备的IDFA MD5值 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *idfaMd5;

/** Android设备IMEI MD5值，iOS设备填空 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *imeiMd5;

/** Android设备的android_id MD5值，iOS设备填空 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *androidIdMd5;

/** Android设备Google advertising ID (GAID) MD5值 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *gaidMd5;

/** msa 联盟 oaid 匿名设备标识符(Open Anonymous Device Identifier) MD5值 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *oaidMd5;

/** Android设备IMEI1 MD5值 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *imei1Md5;

/** Android设备IMEI2 MD5值 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *imei2Md5;

/** 是否是自定义idfa */
@property(nonatomic, readwrite) BOOL isCustomIdfa;

/** 是否是自定义地理位置 */
@property(nonatomic, readwrite) BOOL isCustomLocation;

/** 是否是自定义idfv */
@property(nonatomic, readwrite) BOOL isCustomIdfv;

@end

#pragma mark - SigmobSize

typedef WindGPB_ENUM(SigmobSize_FieldNumber) {
  SigmobSize_FieldNumber_Width = 1,
  SigmobSize_FieldNumber_Height = 2,
};

/**
 * 二维尺寸信息
 **/
WindGPB_FINAL @interface SigmobSize : WindGPBMessage

/** 宽度, 必填！ */
@property(nonatomic, readwrite) uint32_t width;

/** 高度, 必填！ */
@property(nonatomic, readwrite) uint32_t height;

@end

#pragma mark - SigmobNetwork

typedef WindGPB_ENUM(SigmobNetwork_FieldNumber) {
  SigmobNetwork_FieldNumber_Ipv4 = 1,
  SigmobNetwork_FieldNumber_ConnectionType = 2,
  SigmobNetwork_FieldNumber_OperatorType = 3,
  SigmobNetwork_FieldNumber_Ua = 4,
  SigmobNetwork_FieldNumber_Operator_p = 5,
  SigmobNetwork_FieldNumber_Mac = 6,
  SigmobNetwork_FieldNumber_WifiMac = 7,
  SigmobNetwork_FieldNumber_WifiId = 8,
  SigmobNetwork_FieldNumber_Ips = 9,
  SigmobNetwork_FieldNumber_CarrierName = 10,
};

/**
 * 网络环境信息
 **/
WindGPB_FINAL @interface SigmobNetwork : WindGPBMessage

/** 必填！用户设备的公网IPv4地址，格式要求：*************** */
@property(nonatomic, readwrite, copy, null_resettable) NSString *ipv4;

/** 必填！网络连接类型，用于判断网速。0=无法探测当前网络状态; 1=蜂窝数据接入，未知网络类型; 2=2G; 3=3G; 4=4G; 5=5G; 100=Wi-Fi网络接入; 101=以太网接入 */
@property(nonatomic, readwrite) uint32_t connectionType;

/** 必填！移动运营商类型，用于运营商定向广告。0=未知的运营商；1=中国移动；2=中国联通；3=中国电信； */
@property(nonatomic, readwrite) uint32_t operatorType;

/** 浏览器ua */
@property(nonatomic, readwrite, copy, null_resettable) NSString *ua;

/** 必填！移动运营商类型（将来会废弃operator_type）46011 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *operator_p;

/** 设备的mac地址 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *mac;

/** wifi路由器的mac地址 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *wifiMac;

/** wifi ssid */
@property(nonatomic, readwrite, copy, null_resettable) NSString *wifiId;

/** 设备各种ip取值集合（仅iOS使用） */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableDictionary<NSString*, NSString*> *ips;
/** The number of items in @c ips without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger ips_Count;

/** 运营商名称 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *carrierName;

@end

#pragma mark - SigmobVideo

typedef WindGPB_ENUM(SigmobVideo_FieldNumber) {
  SigmobVideo_FieldNumber_MaxDuration = 1,
  SigmobVideo_FieldNumber_MinDuration = 2,
};

WindGPB_FINAL @interface SigmobVideo : WindGPBMessage

/** 视频最大时长 */
@property(nonatomic, readwrite) uint32_t maxDuration;

/** 视频最小时长 */
@property(nonatomic, readwrite) uint32_t minDuration;

@end

#pragma mark - SigmobHeaderBidding

typedef WindGPB_ENUM(SigmobHeaderBidding_FieldNumber) {
  SigmobHeaderBidding_FieldNumber_BidToken = 1,
  SigmobHeaderBidding_FieldNumber_ChannelId = 2,
  SigmobHeaderBidding_FieldNumber_Options = 3,
  SigmobHeaderBidding_FieldNumber_PId = 4,
  SigmobHeaderBidding_FieldNumber_Cur = 5,
  SigmobHeaderBidding_FieldNumber_AdType = 6,
  SigmobHeaderBidding_FieldNumber_Thrmei = 7,
};

WindGPB_FINAL @interface SigmobHeaderBidding : WindGPBMessage

/** 竞价获取广告标识 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *bidToken;

/** 聚合渠道id */
@property(nonatomic, readwrite) uint32_t channelId;

/** 外部渠道媒体属性 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableDictionary<NSString*, NSString*> *options;
/** The number of items in @c options without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger options_Count;

/** 竞价广告位 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *pId;

/** 请求价格币种 CNY, USD */
@property(nonatomic, readwrite, copy, null_resettable) NSString *cur;

/** 聚合的广告类型（注意sig 插屏就是4） sdk3.8.0support */
@property(nonatomic, readwrite) int32_t adType;

/** gdt thrmei 信息  【sdk4.3.0support】 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *thrmei;

@end

#pragma mark - SigmobPrivacy

typedef WindGPB_ENUM(SigmobPrivacy_FieldNumber) {
  SigmobPrivacy_FieldNumber_GdprConsent = 1,
  SigmobPrivacy_FieldNumber_ChildProtection = 2,
  SigmobPrivacy_FieldNumber_Age = 3,
};

WindGPB_FINAL @interface SigmobPrivacy : WindGPBMessage

/** gdpr 授权状态 */
@property(nonatomic, readwrite) uint32_t gdprConsent;

/** 儿童保护 */
@property(nonatomic, readwrite) uint32_t childProtection;

/** 用户年龄 */
@property(nonatomic, readwrite) uint32_t age;

@end

#pragma mark - SigmobColor

typedef WindGPB_ENUM(SigmobColor_FieldNumber) {
  SigmobColor_FieldNumber_Red = 1,
  SigmobColor_FieldNumber_Green = 2,
  SigmobColor_FieldNumber_Blue = 3,
  SigmobColor_FieldNumber_Alpha = 4,
};

WindGPB_FINAL @interface SigmobColor : WindGPBMessage

/** 红色值，整数 0-255 */
@property(nonatomic, readwrite) uint32_t red;

/** 绿色值，整数 0-255 */
@property(nonatomic, readwrite) uint32_t green;

/** 蓝色值，整数 0-255 */
@property(nonatomic, readwrite) uint32_t blue;

/** 透明度，小数 0.0-1.0 */
@property(nonatomic, readwrite) float alpha;

@end

#pragma mark - SigmobUser

typedef WindGPB_ENUM(SigmobUser_FieldNumber) {
  SigmobUser_FieldNumber_Yob = 1,
  SigmobUser_FieldNumber_Gender = 2,
  SigmobUser_FieldNumber_AppListArray = 3,
  SigmobUser_FieldNumber_UserStrategyArray = 4,
  SigmobUser_FieldNumber_AppMarketVersion = 5,
  SigmobUser_FieldNumber_IsMinor = 7,
  SigmobUser_FieldNumber_DisablePersonalizedRecommendation = 8,
  SigmobUser_FieldNumber_ChangeRecommendationState = 9,
};

WindGPB_FINAL @interface SigmobUser : WindGPBMessage

/** 生日年份，例：1995 */
@property(nonatomic, readwrite) uint32_t yob;

/** 性别，M-男；F-女；O-未知 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *gender;

/** 用户已安装应用列表 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<NSString*> *appListArray;
/** The number of items in @c appListArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger appListArray_Count;

/** 用户属性id，如：3342，表示改用户安装了com.weibo应用；1111，表示该用户是体育运动爱好者 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<NSString*> *userStrategyArray;
/** The number of items in @c userStrategyArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger userStrategyArray_Count;

/** 设备应用市场版本号 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVersion *appMarketVersion;
/** Test to see if @c appMarketVersion has been set. */
@property(nonatomic, readwrite) BOOL hasAppMarketVersion;

/** 是否未成年人，false-否；true-是 */
@property(nonatomic, readwrite) BOOL isMinor;

/** 是否禁止个性化推荐，false-不禁止；true-禁止 */
@property(nonatomic, readwrite) BOOL disablePersonalizedRecommendation;

/** disable_personalized_recommendation 个性化状态（实时状态）与sdk初始化个性化推荐状态是否一致。false-一致；true-不一致 */
@property(nonatomic, readwrite) BOOL changeRecommendationState;

@end

#pragma mark - SigmobWXProgramReq

typedef WindGPB_ENUM(SigmobWXProgramReq_FieldNumber) {
  SigmobWXProgramReq_FieldNumber_WxApiVer = 1,
  SigmobWXProgramReq_FieldNumber_OpensdkVer = 2,
  SigmobWXProgramReq_FieldNumber_WxInstalled = 3,
};

WindGPB_FINAL @interface SigmobWXProgramReq : WindGPBMessage

/** 用户微信内SDK版本 */
@property(nonatomic, readwrite) uint32_t wxApiVer;

/** 微信openSDK版本 android: 620953856 ios: 1.8.6 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *opensdkVer;

/** 用户是否已安装微信 */
@property(nonatomic, readwrite) BOOL wxInstalled;

@end

NS_ASSUME_NONNULL_END

CF_EXTERN_C_END

#pragma clang diagnostic pop

// @@protoc_insertion_point(global_scope)
