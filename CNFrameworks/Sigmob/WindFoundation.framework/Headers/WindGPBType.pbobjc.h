// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/type.proto

// This CPP symbol can be defined to use imports that match up to the framework
// imports needed when using CocoaPods.
#if !defined(WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS)
 #define WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS 0
#endif

#if WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS
 #import <WindFoundation/WindGPBDescriptor.h>
 #import <WindFoundation/WindGPBMessage.h>
 #import <WindFoundation/WindGPBRootObject.h>
#else
 #import "WindGPBDescriptor.h"
 #import "WindGPBMessage.h"
 #import "WindGPBRootObject.h"
#endif

#if GOOGLE_PROTOBUF_OBJC_VERSION < 30004
#error This file was generated by a newer version of protoc which is incompatible with your Protocol Buffer library sources.
#endif
#if 30004 < GOOGLE_PROTOBUF_OBJC_MIN_SUPPORTED_VERSION
#error This file was generated by an older version of protoc which is incompatible with your Protocol Buffer library sources.
#endif

// @@protoc_insertion_point(imports)

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"

CF_EXTERN_C_BEGIN

@class WindGPBAny;
@class WindGPBEnumValue;
@class WindGPBField;
@class WindGPBOption;
@class WindGPBSourceContext;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Enum WindGPBSyntax

/** The syntax in which a protocol buffer element is defined. */
typedef WindGPB_ENUM(WindGPBSyntax) {
  /**
   * Value used if any message's field encounters a value that is not defined
   * by this enum. The message will also have C functions to get/set the rawValue
   * of the field.
   **/
  WindGPBSyntax_WindGPBUnrecognizedEnumeratorValue = kWindGPBUnrecognizedEnumeratorValue,
  /** Syntax `proto2`. */
  WindGPBSyntax_SyntaxProto2 = 0,

  /** Syntax `proto3`. */
  WindGPBSyntax_SyntaxProto3 = 1,
};

WindGPBEnumDescriptor *WindGPBSyntax_EnumDescriptor(void);

/**
 * Checks to see if the given value is defined by the enum or was not known at
 * the time this source was generated.
 **/
BOOL WindGPBSyntax_IsValidValue(int32_t value);

#pragma mark - Enum WindGPBField_Kind

/** Basic field types. */
typedef WindGPB_ENUM(WindGPBField_Kind) {
  /**
   * Value used if any message's field encounters a value that is not defined
   * by this enum. The message will also have C functions to get/set the rawValue
   * of the field.
   **/
  WindGPBField_Kind_WindGPBUnrecognizedEnumeratorValue = kWindGPBUnrecognizedEnumeratorValue,
  /** Field type unknown. */
  WindGPBField_Kind_TypeUnknown = 0,

  /** Field type double. */
  WindGPBField_Kind_TypeDouble = 1,

  /** Field type float. */
  WindGPBField_Kind_TypeFloat = 2,

  /** Field type int64. */
  WindGPBField_Kind_TypeInt64 = 3,

  /** Field type uint64. */
  WindGPBField_Kind_TypeUint64 = 4,

  /** Field type int32. */
  WindGPBField_Kind_TypeInt32 = 5,

  /** Field type fixed64. */
  WindGPBField_Kind_TypeFixed64 = 6,

  /** Field type fixed32. */
  WindGPBField_Kind_TypeFixed32 = 7,

  /** Field type bool. */
  WindGPBField_Kind_TypeBool = 8,

  /** Field type string. */
  WindGPBField_Kind_TypeString = 9,

  /** Field type group. Proto2 syntax only, and deprecated. */
  WindGPBField_Kind_TypeGroup = 10,

  /** Field type message. */
  WindGPBField_Kind_TypeMessage = 11,

  /** Field type bytes. */
  WindGPBField_Kind_TypeBytes = 12,

  /** Field type uint32. */
  WindGPBField_Kind_TypeUint32 = 13,

  /** Field type enum. */
  WindGPBField_Kind_TypeEnum = 14,

  /** Field type sfixed32. */
  WindGPBField_Kind_TypeSfixed32 = 15,

  /** Field type sfixed64. */
  WindGPBField_Kind_TypeSfixed64 = 16,

  /** Field type sint32. */
  WindGPBField_Kind_TypeSint32 = 17,

  /** Field type sint64. */
  WindGPBField_Kind_TypeSint64 = 18,
};

WindGPBEnumDescriptor *WindGPBField_Kind_EnumDescriptor(void);

/**
 * Checks to see if the given value is defined by the enum or was not known at
 * the time this source was generated.
 **/
BOOL WindGPBField_Kind_IsValidValue(int32_t value);

#pragma mark - Enum WindGPBField_Cardinality

/** Whether a field is optional, required, or repeated. */
typedef WindGPB_ENUM(WindGPBField_Cardinality) {
  /**
   * Value used if any message's field encounters a value that is not defined
   * by this enum. The message will also have C functions to get/set the rawValue
   * of the field.
   **/
  WindGPBField_Cardinality_WindGPBUnrecognizedEnumeratorValue = kWindGPBUnrecognizedEnumeratorValue,
  /** For fields with unknown cardinality. */
  WindGPBField_Cardinality_CardinalityUnknown = 0,

  /** For optional fields. */
  WindGPBField_Cardinality_CardinalityOptional = 1,

  /** For required fields. Proto2 syntax only. */
  WindGPBField_Cardinality_CardinalityRequired = 2,

  /** For repeated fields. */
  WindGPBField_Cardinality_CardinalityRepeated = 3,
};

WindGPBEnumDescriptor *WindGPBField_Cardinality_EnumDescriptor(void);

/**
 * Checks to see if the given value is defined by the enum or was not known at
 * the time this source was generated.
 **/
BOOL WindGPBField_Cardinality_IsValidValue(int32_t value);

#pragma mark - WindGPBTypeRoot

/**
 * Exposes the extension registry for this file.
 *
 * The base class provides:
 * @code
 *   + (WindGPBExtensionRegistry *)extensionRegistry;
 * @endcode
 * which is a @c WindGPBExtensionRegistry that includes all the extensions defined by
 * this file and all files that it depends on.
 **/
WindGPB_FINAL @interface WindGPBTypeRoot : WindGPBRootObject
@end

#pragma mark - WindGPBType

typedef WindGPB_ENUM(WindGPBType_FieldNumber) {
  WindGPBType_FieldNumber_Name = 1,
  WindGPBType_FieldNumber_FieldsArray = 2,
  WindGPBType_FieldNumber_OneofsArray = 3,
  WindGPBType_FieldNumber_OptionsArray = 4,
  WindGPBType_FieldNumber_SourceContext = 5,
  WindGPBType_FieldNumber_Syntax = 6,
};

/**
 * A protocol buffer message type.
 **/
WindGPB_FINAL @interface WindGPBType : WindGPBMessage

/** The fully qualified message name. */
@property(nonatomic, readwrite, copy, null_resettable) NSString *name;

/** The list of fields. */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<WindGPBField*> *fieldsArray;
/** The number of items in @c fieldsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger fieldsArray_Count;

/** The list of types appearing in `oneof` definitions in this type. */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<NSString*> *oneofsArray;
/** The number of items in @c oneofsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger oneofsArray_Count;

/** The protocol buffer options. */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<WindGPBOption*> *optionsArray;
/** The number of items in @c optionsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger optionsArray_Count;

/** The source context. */
@property(nonatomic, readwrite, strong, null_resettable) WindGPBSourceContext *sourceContext;
/** Test to see if @c sourceContext has been set. */
@property(nonatomic, readwrite) BOOL hasSourceContext;

/** The source syntax. */
@property(nonatomic, readwrite) WindGPBSyntax syntax;

@end

/**
 * Fetches the raw value of a @c WindGPBType's @c syntax property, even
 * if the value was not defined by the enum at the time the code was generated.
 **/
int32_t WindGPBType_Syntax_RawValue(WindGPBType *message);
/**
 * Sets the raw value of an @c WindGPBType's @c syntax property, allowing
 * it to be set to a value that was not defined by the enum at the time the code
 * was generated.
 **/
void SetWindGPBType_Syntax_RawValue(WindGPBType *message, int32_t value);

#pragma mark - WindGPBField

typedef WindGPB_ENUM(WindGPBField_FieldNumber) {
  WindGPBField_FieldNumber_Kind = 1,
  WindGPBField_FieldNumber_Cardinality = 2,
  WindGPBField_FieldNumber_Number = 3,
  WindGPBField_FieldNumber_Name = 4,
  WindGPBField_FieldNumber_TypeURL = 6,
  WindGPBField_FieldNumber_OneofIndex = 7,
  WindGPBField_FieldNumber_Packed = 8,
  WindGPBField_FieldNumber_OptionsArray = 9,
  WindGPBField_FieldNumber_JsonName = 10,
  WindGPBField_FieldNumber_DefaultValue = 11,
};

/**
 * A single field of a message type.
 **/
WindGPB_FINAL @interface WindGPBField : WindGPBMessage

/** The field type. */
@property(nonatomic, readwrite) WindGPBField_Kind kind;

/** The field cardinality. */
@property(nonatomic, readwrite) WindGPBField_Cardinality cardinality;

/** The field number. */
@property(nonatomic, readwrite) int32_t number;

/** The field name. */
@property(nonatomic, readwrite, copy, null_resettable) NSString *name;

/**
 * The field type URL, without the scheme, for message or enumeration
 * types. Example: `"type.googleapis.com/google.protobuf.Timestamp"`.
 **/
@property(nonatomic, readwrite, copy, null_resettable) NSString *typeURL;

/**
 * The index of the field type in `Type.oneofs`, for message or enumeration
 * types. The first type has index 1; zero means the type is not in the list.
 **/
@property(nonatomic, readwrite) int32_t oneofIndex;

/** Whether to use alternative packed wire representation. */
@property(nonatomic, readwrite) BOOL packed;

/** The protocol buffer options. */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<WindGPBOption*> *optionsArray;
/** The number of items in @c optionsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger optionsArray_Count;

/** The field JSON name. */
@property(nonatomic, readwrite, copy, null_resettable) NSString *jsonName;

/** The string value of the default value of this field. Proto2 syntax only. */
@property(nonatomic, readwrite, copy, null_resettable) NSString *defaultValue;

@end

/**
 * Fetches the raw value of a @c WindGPBField's @c kind property, even
 * if the value was not defined by the enum at the time the code was generated.
 **/
int32_t WindGPBField_Kind_RawValue(WindGPBField *message);
/**
 * Sets the raw value of an @c WindGPBField's @c kind property, allowing
 * it to be set to a value that was not defined by the enum at the time the code
 * was generated.
 **/
void SetWindGPBField_Kind_RawValue(WindGPBField *message, int32_t value);

/**
 * Fetches the raw value of a @c WindGPBField's @c cardinality property, even
 * if the value was not defined by the enum at the time the code was generated.
 **/
int32_t WindGPBField_Cardinality_RawValue(WindGPBField *message);
/**
 * Sets the raw value of an @c WindGPBField's @c cardinality property, allowing
 * it to be set to a value that was not defined by the enum at the time the code
 * was generated.
 **/
void SetWindGPBField_Cardinality_RawValue(WindGPBField *message, int32_t value);

#pragma mark - WindGPBEnum

typedef WindGPB_ENUM(WindGPBEnum_FieldNumber) {
  WindGPBEnum_FieldNumber_Name = 1,
  WindGPBEnum_FieldNumber_EnumvalueArray = 2,
  WindGPBEnum_FieldNumber_OptionsArray = 3,
  WindGPBEnum_FieldNumber_SourceContext = 4,
  WindGPBEnum_FieldNumber_Syntax = 5,
};

/**
 * Enum type definition.
 **/
WindGPB_FINAL @interface WindGPBEnum : WindGPBMessage

/** Enum type name. */
@property(nonatomic, readwrite, copy, null_resettable) NSString *name;

/** Enum value definitions. */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<WindGPBEnumValue*> *enumvalueArray;
/** The number of items in @c enumvalueArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger enumvalueArray_Count;

/** Protocol buffer options. */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<WindGPBOption*> *optionsArray;
/** The number of items in @c optionsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger optionsArray_Count;

/** The source context. */
@property(nonatomic, readwrite, strong, null_resettable) WindGPBSourceContext *sourceContext;
/** Test to see if @c sourceContext has been set. */
@property(nonatomic, readwrite) BOOL hasSourceContext;

/** The source syntax. */
@property(nonatomic, readwrite) WindGPBSyntax syntax;

@end

/**
 * Fetches the raw value of a @c WindGPBEnum's @c syntax property, even
 * if the value was not defined by the enum at the time the code was generated.
 **/
int32_t WindGPBEnum_Syntax_RawValue(WindGPBEnum *message);
/**
 * Sets the raw value of an @c WindGPBEnum's @c syntax property, allowing
 * it to be set to a value that was not defined by the enum at the time the code
 * was generated.
 **/
void SetWindGPBEnum_Syntax_RawValue(WindGPBEnum *message, int32_t value);

#pragma mark - WindGPBEnumValue

typedef WindGPB_ENUM(WindGPBEnumValue_FieldNumber) {
  WindGPBEnumValue_FieldNumber_Name = 1,
  WindGPBEnumValue_FieldNumber_Number = 2,
  WindGPBEnumValue_FieldNumber_OptionsArray = 3,
};

/**
 * Enum value definition.
 **/
WindGPB_FINAL @interface WindGPBEnumValue : WindGPBMessage

/** Enum value name. */
@property(nonatomic, readwrite, copy, null_resettable) NSString *name;

/** Enum value number. */
@property(nonatomic, readwrite) int32_t number;

/** Protocol buffer options. */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<WindGPBOption*> *optionsArray;
/** The number of items in @c optionsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger optionsArray_Count;

@end

#pragma mark - WindGPBOption

typedef WindGPB_ENUM(WindGPBOption_FieldNumber) {
  WindGPBOption_FieldNumber_Name = 1,
  WindGPBOption_FieldNumber_Value = 2,
};

/**
 * A protocol buffer option, which can be attached to a message, field,
 * enumeration, etc.
 **/
WindGPB_FINAL @interface WindGPBOption : WindGPBMessage

/**
 * The option's name. For protobuf built-in options (options defined in
 * descriptor.proto), this is the short name. For example, `"map_entry"`.
 * For custom options, it should be the fully-qualified name. For example,
 * `"google.api.http"`.
 **/
@property(nonatomic, readwrite, copy, null_resettable) NSString *name;

/**
 * The option's value packed in an Any message. If the value is a primitive,
 * the corresponding wrapper type defined in google/protobuf/wrappers.proto
 * should be used. If the value is an enum, it should be stored as an int32
 * value using the google.protobuf.Int32Value type.
 **/
@property(nonatomic, readwrite, strong, null_resettable) WindGPBAny *value;
/** Test to see if @c value has been set. */
@property(nonatomic, readwrite) BOOL hasValue;

@end

NS_ASSUME_NONNULL_END

CF_EXTERN_C_END

#pragma clang diagnostic pop

// @@protoc_insertion_point(global_scope)
