// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sigmob_bid_request.proto

// This CPP symbol can be defined to use imports that match up to the framework
// imports needed when using CocoaPods.
#if !defined(WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS)
 #define WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS 0
#endif

#if WindGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS
 #import <WindFoundation/WindGPBProtocolBuffers.h>
#else
 #import "WindGPBProtocolBuffers.h"
#endif

#if GOOGLE_PROTOBUF_OBJC_VERSION < 30004
#error This file was generated by a newer version of protoc which is incompatible with your Protocol Buffer library sources.
#endif
#if 30004 < GOOGLE_PROTOBUF_OBJC_MIN_SUPPORTED_VERSION
#error This file was generated by an older version of protoc which is incompatible with your Protocol Buffer library sources.
#endif

// @@protoc_insertion_point(imports)

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"

CF_EXTERN_C_BEGIN

@class SigmobApp;
@class SigmobDevice;
@class SigmobHeaderBidding;
@class SigmobNetwork;
@class SigmobPrivacy;
@class SigmobUser;
@class SigmobVersion;
@class SigmobWXProgramReq;
@class WindSDKAdSlot;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - WindSDKSigmobBidRequestRoot

/**
 * Exposes the extension registry for this file.
 *
 * The base class provides:
 * @code
 *   + (WindGPBExtensionRegistry *)extensionRegistry;
 * @endcode
 * which is a @c WindGPBExtensionRegistry that includes all the extensions defined by
 * this file and all files that it depends on.
 **/
WindGPB_FINAL @interface WindSDKSigmobBidRequestRoot : WindGPBRootObject
@end

#pragma mark - WindSDKBidRequest

typedef WindGPB_ENUM(WindSDKBidRequest_FieldNumber) {
  WindSDKBidRequest_FieldNumber_RequestId = 1,
  WindSDKBidRequest_FieldNumber_ApiVersion = 2,
  WindSDKBidRequest_FieldNumber_App = 3,
  WindSDKBidRequest_FieldNumber_Device = 4,
  WindSDKBidRequest_FieldNumber_Network = 5,
  WindSDKBidRequest_FieldNumber_SlotsArray = 6,
  WindSDKBidRequest_FieldNumber_ReqTimestamp = 7,
  WindSDKBidRequest_FieldNumber_RequestSceneType = 8,
  WindSDKBidRequest_FieldNumber_AdIsExpired = 9,
  WindSDKBidRequest_FieldNumber_Privacy = 10,
  WindSDKBidRequest_FieldNumber_DisableMediation = 11,
  WindSDKBidRequest_FieldNumber_Options = 15,
  WindSDKBidRequest_FieldNumber_HeaderBidding = 16,
  WindSDKBidRequest_FieldNumber_User = 17,
  WindSDKBidRequest_FieldNumber_ExtOptions = 19,
  WindSDKBidRequest_FieldNumber_SdkVersion = 20,
  WindSDKBidRequest_FieldNumber_WxProgramReq = 21,
  WindSDKBidRequest_FieldNumber_WidgetIsNonsupport = 24,
  WindSDKBidRequest_FieldNumber_OriginVid = 26,
  WindSDKBidRequest_FieldNumber_OriginPrice = 27,
};

/**
 * 内部竞价请求
 **/
WindGPB_FINAL @interface WindSDKBidRequest : WindGPBMessage

/** 内部生成；请求唯一标识，[a-zA-Z0-9]{32} */
@property(nonatomic, readwrite, copy, null_resettable) NSString *requestId;

/** 选填, API版本 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVersion *apiVersion;
/** Test to see if @c apiVersion has been set. */
@property(nonatomic, readwrite) BOOL hasApiVersion;

/** 必填！应用信息 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobApp *app;
/** Test to see if @c app has been set. */
@property(nonatomic, readwrite) BOOL hasApp;

/** 必填！设备信息 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobDevice *device;
/** Test to see if @c device has been set. */
@property(nonatomic, readwrite) BOOL hasDevice;

/** 必填！网络环境信息 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobNetwork *network;
/** Test to see if @c network has been set. */
@property(nonatomic, readwrite) BOOL hasNetwork;

/** 必填！广告位信息。 目前只支持单广告位 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableArray<WindSDKAdSlot*> *slotsArray;
/** The number of items in @c slotsArray without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger slotsArray_Count;

/** 广告请求时间，unix时间戳 (10 位，从1970开始的时间戳) */
@property(nonatomic, readwrite) int64_t reqTimestamp;

/** 发起请求时场景，1）前后台切换 2）视频播放中自动加载请求 3）初始化请求 5）关闭中请求 6）时间戳过期再请求 7）主动过期再请求  999）其他被动请求 */
@property(nonatomic, readwrite) uint32_t requestSceneType;

/** 发起请求时已缓存的广告是否过期了 */
@property(nonatomic, readwrite) BOOL adIsExpired;

/** 用户隐私 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobPrivacy *privacy;
/** Test to see if @c privacy has been set. */
@property(nonatomic, readwrite) BOOL hasPrivacy;

/** false: 使用的聚合；true: 单接 */
@property(nonatomic, readwrite) BOOL disableMediation;

/** 保留扩展字段 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableDictionary<NSString*, NSString*> *options;
/** The number of items in @c options without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger options_Count;

/** header bidding信息 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobHeaderBidding *headerBidding;
/** Test to see if @c headerBidding has been set. */
@property(nonatomic, readwrite) BOOL hasHeaderBidding;

/** 用户信息 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobUser *user;
/** Test to see if @c user has been set. */
@property(nonatomic, readwrite) BOOL hasUser;

/** 保留扩展字段 */
@property(nonatomic, readwrite, strong, null_resettable) NSMutableDictionary<NSString*, NSString*> *extOptions;
/** The number of items in @c extOptions without causing the array to be created. */
@property(nonatomic, readonly) NSUInteger extOptions_Count;

/** sdk版本 */
@property(nonatomic, readwrite, strong, null_resettable) SigmobVersion *sdkVersion;
/** Test to see if @c sdkVersion has been set. */
@property(nonatomic, readwrite) BOOL hasSdkVersion;

@property(nonatomic, readwrite, strong, null_resettable) SigmobWXProgramReq *wxProgramReq;
/** Test to see if @c wxProgramReq has been set. */
@property(nonatomic, readwrite) BOOL hasWxProgramReq;

/** iOS是否不支持互动组件 */
@property(nonatomic, readwrite) BOOL widgetIsNonsupport;

/** 过期在请求原始广告曝光id */
@property(nonatomic, readwrite, copy, null_resettable) NSString *originVid;

/** 原始广告结算价格 */
@property(nonatomic, readwrite, copy, null_resettable) NSString *originPrice;

@end

NS_ASSUME_NONNULL_END

CF_EXTERN_C_END

#pragma clang diagnostic pop

// @@protoc_insertion_point(global_scope)
