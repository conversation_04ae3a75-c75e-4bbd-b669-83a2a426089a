<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/NSArray+SMSAdditions.h</key>
		<data>
		qAy8ALyeSY4/4KOsDOE5HpfEhBQ=
		</data>
		<key>Headers/NSArray+SMSShorthandAdditions.h</key>
		<data>
		kizLsdYITi5laxrltaYTCZ5rPVo=
		</data>
		<key>Headers/NSData+SMImageContentType.h</key>
		<data>
		BjI9jhNDFN2C4DTmNK2GI2Ef2lI=
		</data>
		<key>Headers/NSData+WindConverter.h</key>
		<data>
		bXD/vfye0D5qoa5W7WworpNcUZg=
		</data>
		<key>Headers/NSData+WindCrypt.h</key>
		<data>
		H+JYnlBmaeMzPT9h7IhfatKGT6Q=
		</data>
		<key>Headers/NSData+WindPadding.h</key>
		<data>
		9TQOEck3UxRzYJhvdrr/WRCxzDo=
		</data>
		<key>Headers/NSDictionary+SMObjNil.h</key>
		<data>
		XGaaEht0YuxqJIyht6fhCiemCY0=
		</data>
		<key>Headers/NSFileManager+SigmobFileSize.h</key>
		<data>
		ZvW5f8om2fOSh8oVrEadvvftid4=
		</data>
		<key>Headers/NSLayoutConstraint+SMSDebugAdditions.h</key>
		<data>
		N51V3ZHxG91UNd7bO63jxHvdn+0=
		</data>
		<key>Headers/NSMutableArray+WindConverter.h</key>
		<data>
		aB/66653eBCs74ToZLDRkcgXAc0=
		</data>
		<key>Headers/NSObject+SMClass.h</key>
		<data>
		y3Hm+ezeELql6v3THKWQaH/HC5k=
		</data>
		<key>Headers/NSObject+SMCoder.h</key>
		<data>
		3erbtGbKk2i0/vQKWf7bAd/6mhk=
		</data>
		<key>Headers/NSObject+SMCoding.h</key>
		<data>
		yXOAV6THpdERaMbSuPJVtO/h0v4=
		</data>
		<key>Headers/NSObject+SMKeyValue.h</key>
		<data>
		QL8P5i1gtMBsUDVD97bDWqUY+EM=
		</data>
		<key>Headers/NSObject+SMProperty.h</key>
		<data>
		P1EcnCOxtTWMMHf93h5aYn+lBtU=
		</data>
		<key>Headers/NSString+SMExtension.h</key>
		<data>
		/7MLbo24oVN+37HSLckNxspZvuc=
		</data>
		<key>Headers/NSString+WindConverter.h</key>
		<data>
		Aet3Wtp+uQLbT/78oFFO/bV8GUg=
		</data>
		<key>Headers/SMASLLogger.h</key>
		<data>
		idn5lI+embAzitT88/MW7E37WMg=
		</data>
		<key>Headers/SMAdLogManager.h</key>
		<data>
		PEeQGDKZHwjcfLw99GN5Ls2BE+0=
		</data>
		<key>Headers/SMAlivedThread.h</key>
		<data>
		+4eAdinkz9u76Ta9ny10XDYuof4=
		</data>
		<key>Headers/SMAnimatedImage.h</key>
		<data>
		WafdclvNJUkujsSCYsmTaNwS0b0=
		</data>
		<key>Headers/SMAnimatedImageView.h</key>
		<data>
		aMJCsjnhm2aV8LyvpmNHSDZ+ZsA=
		</data>
		<key>Headers/SMAssertMacros.h</key>
		<data>
		7WkRZYEJi4v5c3aYDNv3Hl47EmA=
		</data>
		<key>Headers/SMClassInfo.h</key>
		<data>
		rpbwaCMFXnmxOv5yYOunXCnPjsM=
		</data>
		<key>Headers/SMCocoaLog.h</key>
		<data>
		5hk6OpvRLgsY0C+lFxxxqXvy1tg=
		</data>
		<key>Headers/SMContextFilterLogFormatter.h</key>
		<data>
		/BfWnhC9zZOzzekZUCgKi5wOPwc=
		</data>
		<key>Headers/SMCustomFormatter.h</key>
		<data>
		TTPQtY1D+QcG5Zs44Dyr1ZYpww4=
		</data>
		<key>Headers/SMDB.h</key>
		<data>
		F7e504smFRq7enDQl/czuPmwp8g=
		</data>
		<key>Headers/SMDatabase.h</key>
		<data>
		zLFVbXDic7CLNqgrnXeo+DpxAKU=
		</data>
		<key>Headers/SMDatabaseAdditions.h</key>
		<data>
		j3pqiTU+0aMMyNDOT2Dv0ABWCq0=
		</data>
		<key>Headers/SMDatabasePool.h</key>
		<data>
		7qebuRHW5UdgogTESKV52+sYcRA=
		</data>
		<key>Headers/SMDatabaseQueue.h</key>
		<data>
		cJGNtlQ0xSju6u+I0jp2y1lJFhI=
		</data>
		<key>Headers/SMDeviceTool.h</key>
		<data>
		WlPvqsURQMl7RxJHHn5M+P3THiU=
		</data>
		<key>Headers/SMDispatchQueueLogFormatter.h</key>
		<data>
		yDqK7FPJozMb5uKNer5/W9fdiBY=
		</data>
		<key>Headers/SMDispatchQueuePool.h</key>
		<data>
		kL1cTgmhigt08WURAgJxmcjLPNw=
		</data>
		<key>Headers/SMExtension.h</key>
		<data>
		4VNVI4iHp16uyLdheeODCtSd6hw=
		</data>
		<key>Headers/SMExtensionConst.h</key>
		<data>
		fGE6WeEowLC75JZ619atADsSmnA=
		</data>
		<key>Headers/SMFileLogger.h</key>
		<data>
		z93kon/XuAqOtIBihzTWF07Pb3k=
		</data>
		<key>Headers/SMFoundation.h</key>
		<data>
		lSvS3vtDqZR3uEq5CImPkeNrNK8=
		</data>
		<key>Headers/SMGCDTimer.h</key>
		<data>
		8xVocD5XDnTvYvYRm+ZuUJ9V4bA=
		</data>
		<key>Headers/SMHTTPRequestSerializer.h</key>
		<data>
		6G2BfkuHdCG+pVT6C1ocH8r9woU=
		</data>
		<key>Headers/SMKeyChain.h</key>
		<data>
		Ucyer2p5q8Z9APqVT3OkWN4xljY=
		</data>
		<key>Headers/SMLocationManager.h</key>
		<data>
		7SSCYhHU3RHmI3/VNLVl7VOELoY=
		</data>
		<key>Headers/SMLog.h</key>
		<data>
		ryTOKMM8BlWOobS3Mjz+FUleg9E=
		</data>
		<key>Headers/SMLogMacros.h</key>
		<data>
		HbhkkOfJU27lncBVSfuE0QkRM9M=
		</data>
		<key>Headers/SMLogManager.h</key>
		<data>
		oTPl8pIaZO1EQmwV+RtwtcT4EaU=
		</data>
		<key>Headers/SMMasonry.h</key>
		<data>
		iZo7kDAEQSb4L8o4Uhs6B44C4hQ=
		</data>
		<key>Headers/SMMotionManager.h</key>
		<data>
		XirM3Hu6KXC7fc+xZ813Y/ZyoJg=
		</data>
		<key>Headers/SMMultiFormatter.h</key>
		<data>
		bRI7fSMmZhwDcJCVlJJD1CHM1dg=
		</data>
		<key>Headers/SMNetworkManager.h</key>
		<data>
		eo5JdrYFDVlbgxAt/1LG9+2uSU8=
		</data>
		<key>Headers/SMOSLogger.h</key>
		<data>
		rbeJdUrW73esiaTmLJsLzfQ5Il8=
		</data>
		<key>Headers/SMProperty.h</key>
		<data>
		Bm6EJVI32QaF99Gy+79E3IVoJ1A=
		</data>
		<key>Headers/SMPropertyKey.h</key>
		<data>
		srmNSob0cw5BKVKjWiF4bQOrWCA=
		</data>
		<key>Headers/SMPropertyType.h</key>
		<data>
		GqfxN/Htp4x/msVvoW4IoYswmSQ=
		</data>
		<key>Headers/SMRequestManager.h</key>
		<data>
		dsLknTHT5LhSJNWU8BNv8vZlB/4=
		</data>
		<key>Headers/SMResultSet.h</key>
		<data>
		+M7effbqwVhXZXEGWG3M6jXJJBg=
		</data>
		<key>Headers/SMSCompositeConstraint.h</key>
		<data>
		oy666X0iOC3iAPdF46FZ6xQ2/RM=
		</data>
		<key>Headers/SMSConstraint+Private.h</key>
		<data>
		YqXeiKQp9AbcfBVG0FmpO9/Gpo4=
		</data>
		<key>Headers/SMSConstraint.h</key>
		<data>
		/Fucs40Qt0y/ralHDRZ5opq+xlA=
		</data>
		<key>Headers/SMSConstraintMaker.h</key>
		<data>
		mQdksTHmSaPNkG9pxJmVz43DztI=
		</data>
		<key>Headers/SMSLayoutConstraint.h</key>
		<data>
		n+450Vack2QW9IhQ+o98FvY+RlY=
		</data>
		<key>Headers/SMSUtilities.h</key>
		<data>
		F2GlQvm4BIEEM2oEjTqfj5qK0As=
		</data>
		<key>Headers/SMSViewAttribute.h</key>
		<data>
		SBeAkAc+jNh2cTKQ5wTZGPt6VOw=
		</data>
		<key>Headers/SMSViewConstraint.h</key>
		<data>
		HGW2DmODZ7q+Ap+Tj9ToM7AG5BQ=
		</data>
		<key>Headers/SMSafeMutableDictionary.h</key>
		<data>
		8uOUug12YXekPAAoFnggtwEB1kA=
		</data>
		<key>Headers/SMTTYLogger.h</key>
		<data>
		9Byvpja3JFv/qfuVCesEqrvcq8Q=
		</data>
		<key>Headers/SMThreadSafeArray.h</key>
		<data>
		hjsF1fv+A0l81HMLTqgGFs64GQY=
		</data>
		<key>Headers/SMThreadSafeDictionary.h</key>
		<data>
		sBvGiOF/BFK/rHd/uWpR5XLeS5A=
		</data>
		<key>Headers/SMTrackingQueueManager.h</key>
		<data>
		eguwhRNNP+7CEmnxgXqBmzl/lrI=
		</data>
		<key>Headers/SMURLSessionManager.h</key>
		<data>
		0IHSus2Hwrtta46O9WYXczw87YI=
		</data>
		<key>Headers/SMWebImageCoderHelper.h</key>
		<data>
		JFrenNj6l580MDsDrBuboN3tc7E=
		</data>
		<key>Headers/SMWebImageFrame.h</key>
		<data>
		lm6XEl5dpgMMh1N5fh3L5WvAVWQ=
		</data>
		<key>Headers/SMWebImageManager.h</key>
		<data>
		sVD/U4KTRZIS9Q1UZ1qTHRDNIFU=
		</data>
		<key>Headers/SigNVHFile.h</key>
		<data>
		r3XIgiw1XGh9Vk/g4l8Cosui9wo=
		</data>
		<key>Headers/SigNVHGzipFile.h</key>
		<data>
		rU8dzEiLjGb8IvaeY8Sy7DwzgQc=
		</data>
		<key>Headers/SigNVHTarFile.h</key>
		<data>
		5tg9RpqCRzXo68y56i+g/I/Fmpg=
		</data>
		<key>Headers/SigNVHTarGzip.h</key>
		<data>
		QwbH0B7ObIfXBAmM26c7fPl6dOE=
		</data>
		<key>Headers/SigmobAdslot.pbobjc.h</key>
		<data>
		quDCtZZ2FzTBYpyKKGY5ipCYme0=
		</data>
		<key>Headers/SigmobBidRequest.pbobjc.h</key>
		<data>
		TeWoI4+QqT+/YnU4z2JfhqWO604=
		</data>
		<key>Headers/SigmobCommon.pbobjc.h</key>
		<data>
		Ae4h2Fe8OTLnJ9EuqW6uDqoPUFM=
		</data>
		<key>Headers/SigmobDeviceInfoConfig.h</key>
		<data>
		P0N3cqNJc1T2ZQ7ilqB2PcPMzj4=
		</data>
		<key>Headers/SigmobLog.h</key>
		<data>
		mGGQnDnE6mhVRKkyDdRLUR7NiaU=
		</data>
		<key>Headers/SigmobProtoFactory.h</key>
		<data>
		jfvlI1dV1gIEYA7WhgwOiT3w4hg=
		</data>
		<key>Headers/UIColor+SMColor.h</key>
		<data>
		tZJfIpfgXQIu5wRhvHI6JrppPW8=
		</data>
		<key>Headers/UIImage+SMImage.h</key>
		<data>
		FJ7hXDYaU7LcgatcZ/68eSoi9d8=
		</data>
		<key>Headers/UIImageView+SMWebCache.h</key>
		<data>
		yx7sS65xVQNfT1EIxxOsHuRqc0I=
		</data>
		<key>Headers/UIScrollView+WindPlayer.h</key>
		<data>
		HGymX/SxAEQZQm7Ba03vkI7bEQg=
		</data>
		<key>Headers/UIView+WindConverter.h</key>
		<data>
		YZWCP6fse30YtY9brQiyxSl9kIo=
		</data>
		<key>Headers/UIView+WindViewTransition.h</key>
		<data>
		HWPslwr0nysTL85VPu5WlFpJ6tY=
		</data>
		<key>Headers/UIViewController+ZPTransition.h</key>
		<data>
		9s/1p0Tfk8KNmPmnatUfqadmos0=
		</data>
		<key>Headers/UIViewController+ZPTransitionProperty.h</key>
		<data>
		yTxbtMDThGHq/61Mv1QaLSv1IaY=
		</data>
		<key>Headers/View+SMSAdditions.h</key>
		<data>
		spt5Gygpm6PVJoMW/gPtK5KWPf8=
		</data>
		<key>Headers/View+SMSShorthandAdditions.h</key>
		<data>
		ZyHAN6XTwjJ1+c0OmzWs1FF380A=
		</data>
		<key>Headers/ViewController+SMSAdditions.h</key>
		<data>
		FinrL4BXNy+7RMOPzLxzCZJ/QZE=
		</data>
		<key>Headers/WFFCustomDevProtocol.h</key>
		<data>
		Ps0AEa1QcaMpEOQfzD3RYOv162E=
		</data>
		<key>Headers/WindAVPlayerManager.h</key>
		<data>
		mKARQPthvQ5rNkCNriV/RGlwm6M=
		</data>
		<key>Headers/WindAsyncSocket.h</key>
		<data>
		86M11Qyz3utTwKQ4LjjeYWpgEEE=
		</data>
		<key>Headers/WindBaseLog.h</key>
		<data>
		AnEVTxvayaHk4H0p/J3TZBJZB+0=
		</data>
		<key>Headers/WindCocoaHTTPServer.h</key>
		<data>
		Ci0JwcX6MkFTupbNncWiRoGHnl8=
		</data>
		<key>Headers/WindCommonLog.h</key>
		<data>
		FFymN7lLEVMUv55VQKDsCF4VINw=
		</data>
		<key>Headers/WindCryptDefines.h</key>
		<data>
		srNzHkhsdxplEu7TMkFTGf2jOH0=
		</data>
		<key>Headers/WindDDData.h</key>
		<data>
		8ag+z4+hL41xtFgglmA9oQUp2ec=
		</data>
		<key>Headers/WindDDNumber.h</key>
		<data>
		Xk2cJm3G/GaDQlG+t4hH8+BAqtE=
		</data>
		<key>Headers/WindDDRange.h</key>
		<data>
		7WC0IM/30MF/zoGdVP9RhPAU3xU=
		</data>
		<key>Headers/WindDcLog.h</key>
		<data>
		bUCWOhdarU18Kh+W/qAPueFkQMc=
		</data>
		<key>Headers/WindEnum.h</key>
		<data>
		5tyWj7MfQjXa1kQsVCC4Q2zufyI=
		</data>
		<key>Headers/WindFloatView.h</key>
		<data>
		1JjLUL2u0B1XV+JBfbGO+q7NU5E=
		</data>
		<key>Headers/WindFoundation.h</key>
		<data>
		TBOGEttiwlTpLEd2BSP7NexWi1w=
		</data>
		<key>Headers/WindFoundationSDK.h</key>
		<data>
		uxaFEU7rwVDcCHnjj5KlhDFIwTQ=
		</data>
		<key>Headers/WindGPBAny.pbobjc.h</key>
		<data>
		2f6TVP2jBfTUNQS7421heq3coXw=
		</data>
		<key>Headers/WindGPBApi.pbobjc.h</key>
		<data>
		z2pY5Mat1PXsrhQYvlxHHnwxnZA=
		</data>
		<key>Headers/WindGPBArray.h</key>
		<data>
		igk1PO3WFUQ/c0qRQsgkoVyRy2U=
		</data>
		<key>Headers/WindGPBArray_PackagePrivate.h</key>
		<data>
		hyuEgDrR3QY2Eoaa7s2O5m4pi8g=
		</data>
		<key>Headers/WindGPBBootstrap.h</key>
		<data>
		EJtB0SsBXmS5g5kCrKGM8jOxri8=
		</data>
		<key>Headers/WindGPBCodedInputStream.h</key>
		<data>
		rgfOFigikezRun8svhdbLFOqz90=
		</data>
		<key>Headers/WindGPBCodedInputStream_PackagePrivate.h</key>
		<data>
		PeJw4fVodvu9eoKYkHZaY4MnQkM=
		</data>
		<key>Headers/WindGPBCodedOutputStream.h</key>
		<data>
		BOOWI//v5BALyh3du/FAhgmnwbE=
		</data>
		<key>Headers/WindGPBCodedOutputStream_PackagePrivate.h</key>
		<data>
		cobSbb9Qcs8FllJlHHY0uhiB6ts=
		</data>
		<key>Headers/WindGPBDescriptor.h</key>
		<data>
		NW6KQLYi/nFnEcXk02NLKezfTBw=
		</data>
		<key>Headers/WindGPBDescriptor_PackagePrivate.h</key>
		<data>
		ovGPZvULDHwJV0NnRxyQE+QE+0o=
		</data>
		<key>Headers/WindGPBDictionary.h</key>
		<data>
		81G9fRP90hmP3Rm0RiBuzYqBw68=
		</data>
		<key>Headers/WindGPBDictionary_PackagePrivate.h</key>
		<data>
		lODFZAxRJlB7dCRx2o6dkY6xxck=
		</data>
		<key>Headers/WindGPBDuration.pbobjc.h</key>
		<data>
		DvsY0zHtqj1pPgmeOJSx/wJ4zY0=
		</data>
		<key>Headers/WindGPBEmpty.pbobjc.h</key>
		<data>
		dN2GCB1LtGaWePfAGatAJewl6YE=
		</data>
		<key>Headers/WindGPBExtensionInternals.h</key>
		<data>
		/PnnpOWqO74EHxWiPe4co4a3e3k=
		</data>
		<key>Headers/WindGPBExtensionRegistry.h</key>
		<data>
		ajsJWaTVYnr7ZbVmeepjf5hUfM8=
		</data>
		<key>Headers/WindGPBFieldMask.pbobjc.h</key>
		<data>
		NIOsxZtguaLhae/r9ZlF/b8epa4=
		</data>
		<key>Headers/WindGPBMessage.h</key>
		<data>
		wUGvrpQ7E4vgfZfsgR3XcSysZD0=
		</data>
		<key>Headers/WindGPBMessage_PackagePrivate.h</key>
		<data>
		WFQbTY2feybXydOkT7u1w8ZWWwQ=
		</data>
		<key>Headers/WindGPBProtocolBuffers.h</key>
		<data>
		AqJbguedMQtt+f874NDcfbiZ0co=
		</data>
		<key>Headers/WindGPBProtocolBuffers_RuntimeSupport.h</key>
		<data>
		sXsczE2aU06Qb54wtT0zAqlnN+U=
		</data>
		<key>Headers/WindGPBRootObject.h</key>
		<data>
		XXdjW0fMm0V9tYjLP6tnXfizRMQ=
		</data>
		<key>Headers/WindGPBRootObject_PackagePrivate.h</key>
		<data>
		W8ytbkI6ocbYmagFcY+ex7hVIvA=
		</data>
		<key>Headers/WindGPBRuntimeTypes.h</key>
		<data>
		9Aglwo0WqO9HLTwBbj9RdD8ql74=
		</data>
		<key>Headers/WindGPBSourceContext.pbobjc.h</key>
		<data>
		Sk7gC36adFLBeSSgIuFPHmWO9Bc=
		</data>
		<key>Headers/WindGPBStruct.pbobjc.h</key>
		<data>
		vuqBhRtXCv7xgbaOzH5NGWSVXCI=
		</data>
		<key>Headers/WindGPBTimestamp.pbobjc.h</key>
		<data>
		gbL6+uqkchD41rvhkjNlqBVzk6k=
		</data>
		<key>Headers/WindGPBType.pbobjc.h</key>
		<data>
		B2+OJzcVoDunZlrc8z6Vo+Mc8nM=
		</data>
		<key>Headers/WindGPBUnknownField.h</key>
		<data>
		x3gQeujDE4xtNFNABDqJl+IaPIk=
		</data>
		<key>Headers/WindGPBUnknownFieldSet.h</key>
		<data>
		G60Sl6lwIjbaT8Ks17kDZGU1wso=
		</data>
		<key>Headers/WindGPBUnknownFieldSet_PackagePrivate.h</key>
		<data>
		mRVkqT+KThQshuYrdfM+djWTsF8=
		</data>
		<key>Headers/WindGPBUnknownField_PackagePrivate.h</key>
		<data>
		jN6Qlv0S3zOsCgYjUTnGw9NxWTw=
		</data>
		<key>Headers/WindGPBUtilities.h</key>
		<data>
		KnvXxD1OJ8zJs6GacX842MGC6SQ=
		</data>
		<key>Headers/WindGPBUtilities_PackagePrivate.h</key>
		<data>
		Z+zbkaTHa67F4xgPpuo61lMVImg=
		</data>
		<key>Headers/WindGPBWellKnownTypes.h</key>
		<data>
		xSH5Qh1kZ/7iz48cHzdm2b5FHu8=
		</data>
		<key>Headers/WindGPBWireFormat.h</key>
		<data>
		10PVgk6a12azkMHoyuJlhcSCJM0=
		</data>
		<key>Headers/WindGPBWrappers.pbobjc.h</key>
		<data>
		LmCzEb/Kaoip12CrThzDd7AcFWM=
		</data>
		<key>Headers/WindHCDataCacheItem.h</key>
		<data>
		Jq1zYNC3Ox+A2/WhngeVxr+MKUo=
		</data>
		<key>Headers/WindHCDataLoader.h</key>
		<data>
		78UXll75kfJ7O/497o1fRVlRTjw=
		</data>
		<key>Headers/WindHCDataReader.h</key>
		<data>
		i0+vAbXurDe2da41vfZpd5OvjV0=
		</data>
		<key>Headers/WindHCDataRequest.h</key>
		<data>
		Fj20mhuYW2UOHdT+qH3TtT1Zmpo=
		</data>
		<key>Headers/WindHCDataResponse.h</key>
		<data>
		+KpJsHFw9bSuEWf4BRuCN1NoBBM=
		</data>
		<key>Headers/WindHCHTTPConnection.h</key>
		<data>
		HWQXDYPm7x18gIyK2lqWRndMOaM=
		</data>
		<key>Headers/WindHCHTTPHeader.h</key>
		<data>
		Lriar2zw1fzZj4HVkmSeOUe0Acg=
		</data>
		<key>Headers/WindHCHTTPResponse.h</key>
		<data>
		Mckio8V+pZ0Ckv8zVF6fMIP+Sxg=
		</data>
		<key>Headers/WindHCHTTPServer.h</key>
		<data>
		xL7ZDkNGGQgqNM4xmqjWcFVK/0I=
		</data>
		<key>Headers/WindHCRange.h</key>
		<data>
		Sa/gBndC7nP5XAq8WSvmveQnEA0=
		</data>
		<key>Headers/WindHTTPAsyncFileResponse.h</key>
		<data>
		P34UCKCW9iS3X16mZn7BT6OxOsU=
		</data>
		<key>Headers/WindHTTPAuthenticationRequest.h</key>
		<data>
		OMc5btqZdHbqTWW4HTyZZw++90c=
		</data>
		<key>Headers/WindHTTPCache.h</key>
		<data>
		P8lYeV1ylFVO1IsP8yUvhYQbiQQ=
		</data>
		<key>Headers/WindHTTPConnection.h</key>
		<data>
		PILNAF2qbP1GHaL4Z9NX01b/84w=
		</data>
		<key>Headers/WindHTTPDataResponse.h</key>
		<data>
		o3EyZ9CgYdOy9x9LP4aXxVPXNtk=
		</data>
		<key>Headers/WindHTTPDynamicFileResponse.h</key>
		<data>
		nyqjGnaheRsBThdRs02boP+SVY8=
		</data>
		<key>Headers/WindHTTPErrorResponse.h</key>
		<data>
		lW5AX6rbVx02j5PmILMWxtu6evA=
		</data>
		<key>Headers/WindHTTPFileResponse.h</key>
		<data>
		VnyCYYrTJOFrdHKapB1yFfYafA8=
		</data>
		<key>Headers/WindHTTPLogging.h</key>
		<data>
		jPn4KVj8F3mcNJo/COGCeUnoIeE=
		</data>
		<key>Headers/WindHTTPMessage.h</key>
		<data>
		GEEvEojQ8PR/IdhEgx6ogvv9RCw=
		</data>
		<key>Headers/WindHTTPRedirectResponse.h</key>
		<data>
		1QaZWCrfTKMaBjZnb+Zf3ATnpKM=
		</data>
		<key>Headers/WindHTTPResponse.h</key>
		<data>
		ypH60m4qxlkjBhDmbXEZW3wZfXQ=
		</data>
		<key>Headers/WindHTTPServer.h</key>
		<data>
		TztHp7x472JuzqET1FKyA1E2BV8=
		</data>
		<key>Headers/WindHooks.h</key>
		<data>
		hCbqrdFVuZCS5ARLHWky8bOVCyU=
		</data>
		<key>Headers/WindKVOController.h</key>
		<data>
		nsiaHlGe1N5rDH3MI6OS3Hp0Sxs=
		</data>
		<key>Headers/WindLandscapeViewController.h</key>
		<data>
		Qi9wVfkrzqi9l0kohDtiwnbChTY=
		</data>
		<key>Headers/WindLandscapeWindow.h</key>
		<data>
		F2okvGZ46ipRaRPIvY3/Y9QgUJw=
		</data>
		<key>Headers/WindMultipartFormDataParser.h</key>
		<data>
		cdg+2pXe8wPk/C/P8W2UtBk1DzY=
		</data>
		<key>Headers/WindMultipartMessageHeader.h</key>
		<data>
		wos85bCazTtf0re9XuxwN9DSWH0=
		</data>
		<key>Headers/WindMultipartMessageHeaderField.h</key>
		<data>
		cSUUIQRH1CC1Hpj9EvhdfmZTPVk=
		</data>
		<key>Headers/WindOrientationObserver.h</key>
		<data>
		pUn6cBxL8KM2vPL/Ykg1EQQRQXA=
		</data>
		<key>Headers/WindPersentInteractiveTransition.h</key>
		<data>
		jH+LxaLwP3wjP+mPWbi3PKOIejQ=
		</data>
		<key>Headers/WindPlayer.h</key>
		<data>
		mjunnpc5UCcjLTYsRplTtElPV+4=
		</data>
		<key>Headers/WindPlayerConst.h</key>
		<data>
		Z7f/Yu11S9VfHe4YB01C8eiHJRA=
		</data>
		<key>Headers/WindPlayerController.h</key>
		<data>
		5R5SpPe7ILYLmOXOyXCXRrRWU30=
		</data>
		<key>Headers/WindPlayerGestureControl.h</key>
		<data>
		Jaoi3i7hL6VYmluf9Jo8tBVKU8o=
		</data>
		<key>Headers/WindPlayerLogManager.h</key>
		<data>
		rAjZCAu1wNWjrZLdAbj8HL2gkLw=
		</data>
		<key>Headers/WindPlayerMediaControl.h</key>
		<data>
		g325/WRvo+PDBsOOFE523DXNYr8=
		</data>
		<key>Headers/WindPlayerMediaPlayback.h</key>
		<data>
		a4u5BJ1JaedRvajFRMhfaeZN4VE=
		</data>
		<key>Headers/WindPlayerNotification.h</key>
		<data>
		KGsIx/s3RY1diXD6aFtYbgRbUbU=
		</data>
		<key>Headers/WindPlayerView.h</key>
		<data>
		CqjITfjuU00QUgRKuSZdbcPUVAg=
		</data>
		<key>Headers/WindPointer.h</key>
		<data>
		RXf5hXpWCfXzQeiKbItJCaisTBA=
		</data>
		<key>Headers/WindPortraitViewController.h</key>
		<data>
		4XdE62BJtRQrOOxTBAwE7e4ICAQ=
		</data>
		<key>Headers/WindPresentTransition.h</key>
		<data>
		XlN74itmUWXLyMsUXMbyuxJxt70=
		</data>
		<key>Headers/WindReachabilityManager.h</key>
		<data>
		WzAzjeVHWNkBmOktN5LehoXpCi4=
		</data>
		<key>Headers/WindSelectBuilder.h</key>
		<data>
		8rYIXE7nKDiTtl4Nmu4G2MMdb1E=
		</data>
		<key>Headers/WindShareData.h</key>
		<data>
		1Xn/erEz6aRNbBbLnHv+dRd4uE4=
		</data>
		<key>Headers/WindSqlBuilder.h</key>
		<data>
		ZwiYjCD9lKoCZqnNgRGmjuspcfE=
		</data>
		<key>Headers/WindStorageManager.h</key>
		<data>
		l7gB65MqJ2Z441w8IZ0kygdirbM=
		</data>
		<key>Headers/WindTableBuilder.h</key>
		<data>
		G89SdSImpqtTd4dwTR6hgsnqzCc=
		</data>
		<key>Headers/WindWKProcessPool.h</key>
		<data>
		a6L5LwbxS0YVDlzSTbFNJZ8v+zI=
		</data>
		<key>Headers/WindWKWebViewJavascriptBridge.h</key>
		<data>
		Y7ssTeUoIDj5PVkDoO6VacYwsT4=
		</data>
		<key>Headers/WindWebSocket.h</key>
		<data>
		0UkCKwr2eoaRtFM8cDc/KaDgpK8=
		</data>
		<key>Headers/WindWebView.h</key>
		<data>
		EV99eDTG75xwOuH2ipb6UkhupCE=
		</data>
		<key>Headers/WindWebViewJavascriptBridgeBase.h</key>
		<data>
		Xt8J1+if3cG7Hww8gddHryc2gJI=
		</data>
		<key>Headers/WindWebViewJavascriptBridge_JS.h</key>
		<data>
		rrMAMGUcjJUz1XYYA4nWYGcJ+mE=
		</data>
		<key>Headers/WindWhereBuilder.h</key>
		<data>
		pBOTkoxHWIkuZPnN0H1U+GOgjnA=
		</data>
		<key>Headers/WindmillAnimationTool.h</key>
		<data>
		Ds0hepDEC8hHu7heU4HeqixwN3U=
		</data>
		<key>Headers/ZPTransition.h</key>
		<data>
		81KdJ36DoKmVpxSxe9ohE3x0WaI=
		</data>
		<key>Headers/ZPTransitionManager+ViewMoveAnimation.h</key>
		<data>
		GDleanS9Zcd9kP334TohlwGUWRQ=
		</data>
		<key>Headers/ZPTransitionManager.h</key>
		<data>
		r88Cr635ODWi7uJel4vcK+cr8IA=
		</data>
		<key>Headers/ZPTransitionProperty.h</key>
		<data>
		Fv3l0vRuXQFjukQB6SdG3DsLldo=
		</data>
		<key>Headers/ZPTypedefConfig.h</key>
		<data>
		5lbGJEKBJnRbUW6zlDoHsaKMWKU=
		</data>
		<key>Info.plist</key>
		<data>
		xfScLNU+YSC+DAll3AEwTPVPFO4=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		5zBoJbc6nPg6Msf6u42CI6z1i6U=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		/Ob96ulOlVP7Gztxfa9S4AcuRpg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/NSArray+SMSAdditions.h</key>
		<dict>
			<key>hash</key>
			<data>
			qAy8ALyeSY4/4KOsDOE5HpfEhBQ=
			</data>
			<key>hash2</key>
			<data>
			JKUfhgT1c8K8vR9KUERRMMOCxFJKeB3XSvvmpbLLHvc=
			</data>
		</dict>
		<key>Headers/NSArray+SMSShorthandAdditions.h</key>
		<dict>
			<key>hash</key>
			<data>
			kizLsdYITi5laxrltaYTCZ5rPVo=
			</data>
			<key>hash2</key>
			<data>
			yAeielF+3p06t2JxTbW3hLPkOpAh8Cs6nCMRZ/wXEuM=
			</data>
		</dict>
		<key>Headers/NSData+SMImageContentType.h</key>
		<dict>
			<key>hash</key>
			<data>
			BjI9jhNDFN2C4DTmNK2GI2Ef2lI=
			</data>
			<key>hash2</key>
			<data>
			BBgodIjZgliMcOd60JxI7d4VwsN+yqrTRh+9vlQzNgw=
			</data>
		</dict>
		<key>Headers/NSData+WindConverter.h</key>
		<dict>
			<key>hash</key>
			<data>
			bXD/vfye0D5qoa5W7WworpNcUZg=
			</data>
			<key>hash2</key>
			<data>
			V1+BJqeHeWY9B+pPE4ZPePEwIVIDA9DrBYK3ETvGAOo=
			</data>
		</dict>
		<key>Headers/NSData+WindCrypt.h</key>
		<dict>
			<key>hash</key>
			<data>
			H+JYnlBmaeMzPT9h7IhfatKGT6Q=
			</data>
			<key>hash2</key>
			<data>
			E4cQKwGGo69PQ61L9UVSA06fTkubkn8xCgiDWo8TzrU=
			</data>
		</dict>
		<key>Headers/NSData+WindPadding.h</key>
		<dict>
			<key>hash</key>
			<data>
			9TQOEck3UxRzYJhvdrr/WRCxzDo=
			</data>
			<key>hash2</key>
			<data>
			Srcrn6WuxkTCzCvgOQP7FObGtGUCh8WgYzxhPEi+8jA=
			</data>
		</dict>
		<key>Headers/NSDictionary+SMObjNil.h</key>
		<dict>
			<key>hash</key>
			<data>
			XGaaEht0YuxqJIyht6fhCiemCY0=
			</data>
			<key>hash2</key>
			<data>
			edYNIghK0ivdWuP5AB8rGpJyBc5QGYZGwz2QvPj7N8U=
			</data>
		</dict>
		<key>Headers/NSFileManager+SigmobFileSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZvW5f8om2fOSh8oVrEadvvftid4=
			</data>
			<key>hash2</key>
			<data>
			ILcXh/lEdMVf4jzKjFgZMAU4f6PYEqdaKoDOINGzENg=
			</data>
		</dict>
		<key>Headers/NSLayoutConstraint+SMSDebugAdditions.h</key>
		<dict>
			<key>hash</key>
			<data>
			N51V3ZHxG91UNd7bO63jxHvdn+0=
			</data>
			<key>hash2</key>
			<data>
			zjaou9nw3fZJd7XK7dQU+FYS9S7R0ippkR+bJFSmQ18=
			</data>
		</dict>
		<key>Headers/NSMutableArray+WindConverter.h</key>
		<dict>
			<key>hash</key>
			<data>
			aB/66653eBCs74ToZLDRkcgXAc0=
			</data>
			<key>hash2</key>
			<data>
			cj0hpAaz1o+H58oegv6w5hoJXStcBy8xSr3nHcvOOlE=
			</data>
		</dict>
		<key>Headers/NSObject+SMClass.h</key>
		<dict>
			<key>hash</key>
			<data>
			y3Hm+ezeELql6v3THKWQaH/HC5k=
			</data>
			<key>hash2</key>
			<data>
			AV8cTzhSoSFiJ6AelOcvy24u8HeKBQ+5xCuKYI2Uolo=
			</data>
		</dict>
		<key>Headers/NSObject+SMCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			3erbtGbKk2i0/vQKWf7bAd/6mhk=
			</data>
			<key>hash2</key>
			<data>
			YhUvKHhiUaCpCE1BkpHbH7GVX3QOtmfHf+6F9bt+juI=
			</data>
		</dict>
		<key>Headers/NSObject+SMCoding.h</key>
		<dict>
			<key>hash</key>
			<data>
			yXOAV6THpdERaMbSuPJVtO/h0v4=
			</data>
			<key>hash2</key>
			<data>
			SYad5F84Y1LOrbU1oEB0Nszfjp7kmW3zKnf4V9t0S+M=
			</data>
		</dict>
		<key>Headers/NSObject+SMKeyValue.h</key>
		<dict>
			<key>hash</key>
			<data>
			QL8P5i1gtMBsUDVD97bDWqUY+EM=
			</data>
			<key>hash2</key>
			<data>
			UJkhkim9frm7oGpxjlLmWVupJ7QKp0YfNdg+8ZIPcrI=
			</data>
		</dict>
		<key>Headers/NSObject+SMProperty.h</key>
		<dict>
			<key>hash</key>
			<data>
			P1EcnCOxtTWMMHf93h5aYn+lBtU=
			</data>
			<key>hash2</key>
			<data>
			HyaEwlmi7L59uTgK0oq3tkhUF3P4HJvoFlSkndUZqVw=
			</data>
		</dict>
		<key>Headers/NSString+SMExtension.h</key>
		<dict>
			<key>hash</key>
			<data>
			/7MLbo24oVN+37HSLckNxspZvuc=
			</data>
			<key>hash2</key>
			<data>
			M66R24XZgjyRhAum0lD+9Y2n51d9itAMWhU5fWDfprk=
			</data>
		</dict>
		<key>Headers/NSString+WindConverter.h</key>
		<dict>
			<key>hash</key>
			<data>
			Aet3Wtp+uQLbT/78oFFO/bV8GUg=
			</data>
			<key>hash2</key>
			<data>
			y4euP8ldPDgnT0Ldgqdhspp5sB7Fae0ETUA5YS/JQZk=
			</data>
		</dict>
		<key>Headers/SMASLLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			idn5lI+embAzitT88/MW7E37WMg=
			</data>
			<key>hash2</key>
			<data>
			tKbinw3YVdwohBT7Fuj9pBIBC0ljy9chuDUkXcabwxc=
			</data>
		</dict>
		<key>Headers/SMAdLogManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			PEeQGDKZHwjcfLw99GN5Ls2BE+0=
			</data>
			<key>hash2</key>
			<data>
			4MCZljjw8xo7ecB99hQ35kkGuHS4PpuBx4TLuULd37k=
			</data>
		</dict>
		<key>Headers/SMAlivedThread.h</key>
		<dict>
			<key>hash</key>
			<data>
			+4eAdinkz9u76Ta9ny10XDYuof4=
			</data>
			<key>hash2</key>
			<data>
			hqAuocfOTFYSWUJt99WOhmE1VcbwsBjnkbqcEH5MdI0=
			</data>
		</dict>
		<key>Headers/SMAnimatedImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			WafdclvNJUkujsSCYsmTaNwS0b0=
			</data>
			<key>hash2</key>
			<data>
			l/StD5pmgSDBzqkg+eeFT2pkadCJT0EjfjoefLCVEsQ=
			</data>
		</dict>
		<key>Headers/SMAnimatedImageView.h</key>
		<dict>
			<key>hash</key>
			<data>
			aMJCsjnhm2aV8LyvpmNHSDZ+ZsA=
			</data>
			<key>hash2</key>
			<data>
			UBJdk3XKN04KTJWRMxOGP31ypQtB7zqZBSoAPJerFvk=
			</data>
		</dict>
		<key>Headers/SMAssertMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			7WkRZYEJi4v5c3aYDNv3Hl47EmA=
			</data>
			<key>hash2</key>
			<data>
			S3wPGxmbCroMZu39rdVKrioAopm3U1gT+C7X6yiz9tE=
			</data>
		</dict>
		<key>Headers/SMClassInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			rpbwaCMFXnmxOv5yYOunXCnPjsM=
			</data>
			<key>hash2</key>
			<data>
			j+aR0VoK4vtkrjVJdb/314tbc+MdihuYSMz9EziNRAQ=
			</data>
		</dict>
		<key>Headers/SMCocoaLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			5hk6OpvRLgsY0C+lFxxxqXvy1tg=
			</data>
			<key>hash2</key>
			<data>
			Z8+28CIX22wMJ6ID8ZqqYjK7Y2pQwDBqbB/OAX/gpnU=
			</data>
		</dict>
		<key>Headers/SMContextFilterLogFormatter.h</key>
		<dict>
			<key>hash</key>
			<data>
			/BfWnhC9zZOzzekZUCgKi5wOPwc=
			</data>
			<key>hash2</key>
			<data>
			OIBYE9/yC1yV0hlv9cDTgCkGfLwlBaJhbKnXhBcvJy8=
			</data>
		</dict>
		<key>Headers/SMCustomFormatter.h</key>
		<dict>
			<key>hash</key>
			<data>
			TTPQtY1D+QcG5Zs44Dyr1ZYpww4=
			</data>
			<key>hash2</key>
			<data>
			FVHvhwjNzo+l/kBzmOlMwr45Fnjl4rQOXchxWNz5eZg=
			</data>
		</dict>
		<key>Headers/SMDB.h</key>
		<dict>
			<key>hash</key>
			<data>
			F7e504smFRq7enDQl/czuPmwp8g=
			</data>
			<key>hash2</key>
			<data>
			wZBFgqTG8KBgtayTiHI1PEyXej7iGNK+mmSCvZmyrX8=
			</data>
		</dict>
		<key>Headers/SMDatabase.h</key>
		<dict>
			<key>hash</key>
			<data>
			zLFVbXDic7CLNqgrnXeo+DpxAKU=
			</data>
			<key>hash2</key>
			<data>
			SNpMPWQBZap0dvevElpQ0xWBxu+GBy0hK6OI04OT3MQ=
			</data>
		</dict>
		<key>Headers/SMDatabaseAdditions.h</key>
		<dict>
			<key>hash</key>
			<data>
			j3pqiTU+0aMMyNDOT2Dv0ABWCq0=
			</data>
			<key>hash2</key>
			<data>
			BFdCT4l0b70/maN8JG6+53vS8onPpPFiUiwkH3cMluE=
			</data>
		</dict>
		<key>Headers/SMDatabasePool.h</key>
		<dict>
			<key>hash</key>
			<data>
			7qebuRHW5UdgogTESKV52+sYcRA=
			</data>
			<key>hash2</key>
			<data>
			EWuhHtEdwlsxQNrXhyd/TmHZXCe6Qyl+IZwAzt1q66I=
			</data>
		</dict>
		<key>Headers/SMDatabaseQueue.h</key>
		<dict>
			<key>hash</key>
			<data>
			cJGNtlQ0xSju6u+I0jp2y1lJFhI=
			</data>
			<key>hash2</key>
			<data>
			9rG3KWEQ8shD5/UZiqUpap1p7XyzHvX9xUVuFm5sn1s=
			</data>
		</dict>
		<key>Headers/SMDeviceTool.h</key>
		<dict>
			<key>hash</key>
			<data>
			WlPvqsURQMl7RxJHHn5M+P3THiU=
			</data>
			<key>hash2</key>
			<data>
			k6Kx0kNoxd103jmCG10oNgJMDmTSX5hL5dk7E1vZm18=
			</data>
		</dict>
		<key>Headers/SMDispatchQueueLogFormatter.h</key>
		<dict>
			<key>hash</key>
			<data>
			yDqK7FPJozMb5uKNer5/W9fdiBY=
			</data>
			<key>hash2</key>
			<data>
			mPjz50khCwpuLWzq9gPDj/jt3HhAMvVfYy/C28Ilpts=
			</data>
		</dict>
		<key>Headers/SMDispatchQueuePool.h</key>
		<dict>
			<key>hash</key>
			<data>
			kL1cTgmhigt08WURAgJxmcjLPNw=
			</data>
			<key>hash2</key>
			<data>
			PdRV54/8/lQls/VuJcEuochURuNrQfHqnm2Nc51hfQk=
			</data>
		</dict>
		<key>Headers/SMExtension.h</key>
		<dict>
			<key>hash</key>
			<data>
			4VNVI4iHp16uyLdheeODCtSd6hw=
			</data>
			<key>hash2</key>
			<data>
			xWKvwxIxQhz1rPE1ImI4jkpch/E9tRaeR23xUQ7xlDE=
			</data>
		</dict>
		<key>Headers/SMExtensionConst.h</key>
		<dict>
			<key>hash</key>
			<data>
			fGE6WeEowLC75JZ619atADsSmnA=
			</data>
			<key>hash2</key>
			<data>
			JhI+HbptVQDBQkm9aXFsH9MJxIshOj5OBq6LNPS8bPQ=
			</data>
		</dict>
		<key>Headers/SMFileLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			z93kon/XuAqOtIBihzTWF07Pb3k=
			</data>
			<key>hash2</key>
			<data>
			sa8mvX8YGkZIx1sHGi03MWOwHrSgU6BsxGXOSbVoQ88=
			</data>
		</dict>
		<key>Headers/SMFoundation.h</key>
		<dict>
			<key>hash</key>
			<data>
			lSvS3vtDqZR3uEq5CImPkeNrNK8=
			</data>
			<key>hash2</key>
			<data>
			om5T3a7IJyMuu7ysKUPyUXYYwwTWGQudY/s5lqUsst0=
			</data>
		</dict>
		<key>Headers/SMGCDTimer.h</key>
		<dict>
			<key>hash</key>
			<data>
			8xVocD5XDnTvYvYRm+ZuUJ9V4bA=
			</data>
			<key>hash2</key>
			<data>
			uuffnjNcaR0kSMqM1hV7dv8tS2wEyYtb/Rw7h/Tfgi4=
			</data>
		</dict>
		<key>Headers/SMHTTPRequestSerializer.h</key>
		<dict>
			<key>hash</key>
			<data>
			6G2BfkuHdCG+pVT6C1ocH8r9woU=
			</data>
			<key>hash2</key>
			<data>
			284iZsQtiOYV3q5iT+djtCfB1HpRfB2CuJzmspLZ9WM=
			</data>
		</dict>
		<key>Headers/SMKeyChain.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ucyer2p5q8Z9APqVT3OkWN4xljY=
			</data>
			<key>hash2</key>
			<data>
			smcu8CQz2EODp6POGhOn3V7y3N++dGHHVacS6mdtihA=
			</data>
		</dict>
		<key>Headers/SMLocationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			7SSCYhHU3RHmI3/VNLVl7VOELoY=
			</data>
			<key>hash2</key>
			<data>
			FdKaTrCi557HyPxng7LNjZdVMrvTLfdMYrrrfjU8/FI=
			</data>
		</dict>
		<key>Headers/SMLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			ryTOKMM8BlWOobS3Mjz+FUleg9E=
			</data>
			<key>hash2</key>
			<data>
			loJ75urL3pm+g086i3DJEdJhEH1wziN+UfvdGmaig9s=
			</data>
		</dict>
		<key>Headers/SMLogMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			HbhkkOfJU27lncBVSfuE0QkRM9M=
			</data>
			<key>hash2</key>
			<data>
			cCJhAa5LWEEgKZz81DTNVOGXZ0xVUAeT6aAoFk5jrG0=
			</data>
		</dict>
		<key>Headers/SMLogManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			oTPl8pIaZO1EQmwV+RtwtcT4EaU=
			</data>
			<key>hash2</key>
			<data>
			mfp8rIa1E4lg5wHgOEh6bJAL39GdqGFpbJbZHPbciHo=
			</data>
		</dict>
		<key>Headers/SMMasonry.h</key>
		<dict>
			<key>hash</key>
			<data>
			iZo7kDAEQSb4L8o4Uhs6B44C4hQ=
			</data>
			<key>hash2</key>
			<data>
			GM+myHmcOw44EA5QoF9KU0WZ9dWKaAofafn/Kx4WcfI=
			</data>
		</dict>
		<key>Headers/SMMotionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			XirM3Hu6KXC7fc+xZ813Y/ZyoJg=
			</data>
			<key>hash2</key>
			<data>
			eDE6LuTYvnwNJh1b6xviikckJghWsD424qopqunk4dU=
			</data>
		</dict>
		<key>Headers/SMMultiFormatter.h</key>
		<dict>
			<key>hash</key>
			<data>
			bRI7fSMmZhwDcJCVlJJD1CHM1dg=
			</data>
			<key>hash2</key>
			<data>
			YOfm8EyQU1DKWMfzOmuVXGG/NgcpgwLboA/TvUZRYvU=
			</data>
		</dict>
		<key>Headers/SMNetworkManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			eo5JdrYFDVlbgxAt/1LG9+2uSU8=
			</data>
			<key>hash2</key>
			<data>
			KRXSMG7kMDBJAb+1fC2PFaYtSlfkgxESim/ATvobAfs=
			</data>
		</dict>
		<key>Headers/SMOSLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			rbeJdUrW73esiaTmLJsLzfQ5Il8=
			</data>
			<key>hash2</key>
			<data>
			ORg+7/SO/UlkZ15jP7afsdMN01xYigmmNaSnSOZbgJw=
			</data>
		</dict>
		<key>Headers/SMProperty.h</key>
		<dict>
			<key>hash</key>
			<data>
			Bm6EJVI32QaF99Gy+79E3IVoJ1A=
			</data>
			<key>hash2</key>
			<data>
			TGMCybEscQgm8zwMBkjwbq5/x4g2qS8qWe6MiqN6UyE=
			</data>
		</dict>
		<key>Headers/SMPropertyKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			srmNSob0cw5BKVKjWiF4bQOrWCA=
			</data>
			<key>hash2</key>
			<data>
			k2nKQYdKLoOhXTe9f46LpMY/wsgUcOkKj7q4h7nRKns=
			</data>
		</dict>
		<key>Headers/SMPropertyType.h</key>
		<dict>
			<key>hash</key>
			<data>
			GqfxN/Htp4x/msVvoW4IoYswmSQ=
			</data>
			<key>hash2</key>
			<data>
			z3+2vUYaFGBYVigBwTbgVg1LJ2XvbMYCXNuNFnPLFGk=
			</data>
		</dict>
		<key>Headers/SMRequestManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			dsLknTHT5LhSJNWU8BNv8vZlB/4=
			</data>
			<key>hash2</key>
			<data>
			GESbi0I4MQnNj9tD2EFAyswxdo/konpY7+iVG61/ZXw=
			</data>
		</dict>
		<key>Headers/SMResultSet.h</key>
		<dict>
			<key>hash</key>
			<data>
			+M7effbqwVhXZXEGWG3M6jXJJBg=
			</data>
			<key>hash2</key>
			<data>
			aYSNRwteH191E5FQeVPf57J7wQSu4q4BPsViQmzmjVU=
			</data>
		</dict>
		<key>Headers/SMSCompositeConstraint.h</key>
		<dict>
			<key>hash</key>
			<data>
			oy666X0iOC3iAPdF46FZ6xQ2/RM=
			</data>
			<key>hash2</key>
			<data>
			1ku/PqzejScekhUXLrkhuJ6Xd76CTWTWt4+iur8LCBY=
			</data>
		</dict>
		<key>Headers/SMSConstraint+Private.h</key>
		<dict>
			<key>hash</key>
			<data>
			YqXeiKQp9AbcfBVG0FmpO9/Gpo4=
			</data>
			<key>hash2</key>
			<data>
			vDkFg9iBvmOUNjL0qLgmRRsEoyd9KngCaZMbF5ThlSM=
			</data>
		</dict>
		<key>Headers/SMSConstraint.h</key>
		<dict>
			<key>hash</key>
			<data>
			/Fucs40Qt0y/ralHDRZ5opq+xlA=
			</data>
			<key>hash2</key>
			<data>
			6FUYOhYwZBwxxJZuLuOD+zpu0k9FuzOB6S7Sn8sOqWU=
			</data>
		</dict>
		<key>Headers/SMSConstraintMaker.h</key>
		<dict>
			<key>hash</key>
			<data>
			mQdksTHmSaPNkG9pxJmVz43DztI=
			</data>
			<key>hash2</key>
			<data>
			w7f3j7AEhvR7dUC0Qy8ZO5dL3ZAk3OXbU1E0Ig4lgrM=
			</data>
		</dict>
		<key>Headers/SMSLayoutConstraint.h</key>
		<dict>
			<key>hash</key>
			<data>
			n+450Vack2QW9IhQ+o98FvY+RlY=
			</data>
			<key>hash2</key>
			<data>
			Fz+G+7pIcbnqu2b/uVBunKRrhHxPxPZ/7QRgkB/8AMw=
			</data>
		</dict>
		<key>Headers/SMSUtilities.h</key>
		<dict>
			<key>hash</key>
			<data>
			F2GlQvm4BIEEM2oEjTqfj5qK0As=
			</data>
			<key>hash2</key>
			<data>
			9wS7Il7GuXphRoDztXQNfcJWb9/e55FhrSbNVjt7I68=
			</data>
		</dict>
		<key>Headers/SMSViewAttribute.h</key>
		<dict>
			<key>hash</key>
			<data>
			SBeAkAc+jNh2cTKQ5wTZGPt6VOw=
			</data>
			<key>hash2</key>
			<data>
			3+nkPIspDXOlfPswngqoYhpX/9uDrhTEYIa5/2FoKk8=
			</data>
		</dict>
		<key>Headers/SMSViewConstraint.h</key>
		<dict>
			<key>hash</key>
			<data>
			HGW2DmODZ7q+Ap+Tj9ToM7AG5BQ=
			</data>
			<key>hash2</key>
			<data>
			Ck5lIAKWxlFtR7tvv49YbNfgat2OmBG0KzFvQuS3c6o=
			</data>
		</dict>
		<key>Headers/SMSafeMutableDictionary.h</key>
		<dict>
			<key>hash</key>
			<data>
			8uOUug12YXekPAAoFnggtwEB1kA=
			</data>
			<key>hash2</key>
			<data>
			Ax7KALbPL1CVKqLhdlQgwXtWRXOvFlL87h7rrVRvlFQ=
			</data>
		</dict>
		<key>Headers/SMTTYLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			9Byvpja3JFv/qfuVCesEqrvcq8Q=
			</data>
			<key>hash2</key>
			<data>
			VSF6luTbZpsbSx9ozoTuxr0heb6DXVmUSPpnP7gfWYA=
			</data>
		</dict>
		<key>Headers/SMThreadSafeArray.h</key>
		<dict>
			<key>hash</key>
			<data>
			hjsF1fv+A0l81HMLTqgGFs64GQY=
			</data>
			<key>hash2</key>
			<data>
			LB7jbANgtAUwGL7B56Qe0tIzOjFI4FUTcEdjlBQIxFA=
			</data>
		</dict>
		<key>Headers/SMThreadSafeDictionary.h</key>
		<dict>
			<key>hash</key>
			<data>
			sBvGiOF/BFK/rHd/uWpR5XLeS5A=
			</data>
			<key>hash2</key>
			<data>
			ZAgYiwe2EBVX+vLKPazH3fWpPT2B+hsPvHqLNRHtHZs=
			</data>
		</dict>
		<key>Headers/SMTrackingQueueManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			eguwhRNNP+7CEmnxgXqBmzl/lrI=
			</data>
			<key>hash2</key>
			<data>
			9T8fr6XOT6I/0Ve2ovzyGrUBDLHuFPHG5dcNZ5lhvlI=
			</data>
		</dict>
		<key>Headers/SMURLSessionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			0IHSus2Hwrtta46O9WYXczw87YI=
			</data>
			<key>hash2</key>
			<data>
			GA39nb8LyCgj5mrXTfZmblTcNUgci6Nf6T0EGQJiDeg=
			</data>
		</dict>
		<key>Headers/SMWebImageCoderHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			JFrenNj6l580MDsDrBuboN3tc7E=
			</data>
			<key>hash2</key>
			<data>
			uVimowNUkJ3q+disx/aUwiuepNTr7TItF1WOwlT3hu0=
			</data>
		</dict>
		<key>Headers/SMWebImageFrame.h</key>
		<dict>
			<key>hash</key>
			<data>
			lm6XEl5dpgMMh1N5fh3L5WvAVWQ=
			</data>
			<key>hash2</key>
			<data>
			WzCIYxl+tQRhwdsdAmt+A+JHCSlqOZE2rdZFezWfAM0=
			</data>
		</dict>
		<key>Headers/SMWebImageManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			sVD/U4KTRZIS9Q1UZ1qTHRDNIFU=
			</data>
			<key>hash2</key>
			<data>
			bpcKXs8p2haBoTBLo5GGtDSa78LbgpmLTKFvvtjgmpk=
			</data>
		</dict>
		<key>Headers/SigNVHFile.h</key>
		<dict>
			<key>hash</key>
			<data>
			r3XIgiw1XGh9Vk/g4l8Cosui9wo=
			</data>
			<key>hash2</key>
			<data>
			get/cbCAMCq2FFRFKl/YqiLpct0q/ObjGI9vOhoqL4k=
			</data>
		</dict>
		<key>Headers/SigNVHGzipFile.h</key>
		<dict>
			<key>hash</key>
			<data>
			rU8dzEiLjGb8IvaeY8Sy7DwzgQc=
			</data>
			<key>hash2</key>
			<data>
			1hRJXOG811FKJW4azpyHjo9qNPpT65ZwHFtEWlj6/rU=
			</data>
		</dict>
		<key>Headers/SigNVHTarFile.h</key>
		<dict>
			<key>hash</key>
			<data>
			5tg9RpqCRzXo68y56i+g/I/Fmpg=
			</data>
			<key>hash2</key>
			<data>
			JI2AvomRKfqpO2sLWPTqCXBr8ldH6BZYHfPRzSeSpCI=
			</data>
		</dict>
		<key>Headers/SigNVHTarGzip.h</key>
		<dict>
			<key>hash</key>
			<data>
			QwbH0B7ObIfXBAmM26c7fPl6dOE=
			</data>
			<key>hash2</key>
			<data>
			1fOC6xe8e8FhAsNpzEUbaArcdoLdW9M/Gjk4xfEeXIU=
			</data>
		</dict>
		<key>Headers/SigmobAdslot.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			quDCtZZ2FzTBYpyKKGY5ipCYme0=
			</data>
			<key>hash2</key>
			<data>
			ga5flHaqsn526uh+0UHwIAiTCfF9yF8YwSPsOlZeE5Y=
			</data>
		</dict>
		<key>Headers/SigmobBidRequest.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			TeWoI4+QqT+/YnU4z2JfhqWO604=
			</data>
			<key>hash2</key>
			<data>
			GlEmmBuAZRq2M6lpPTbICbIQoi+vr960E76KgBS8Ub0=
			</data>
		</dict>
		<key>Headers/SigmobCommon.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ae4h2Fe8OTLnJ9EuqW6uDqoPUFM=
			</data>
			<key>hash2</key>
			<data>
			hFSrMexXoFmprTcPDRHmWlbWRL2fldewjaQcV5hHarA=
			</data>
		</dict>
		<key>Headers/SigmobDeviceInfoConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			P0N3cqNJc1T2ZQ7ilqB2PcPMzj4=
			</data>
			<key>hash2</key>
			<data>
			Y6ZtyJQusPwnPOLEPZP0B2GaUwiLs8R3raLNnoPNMtg=
			</data>
		</dict>
		<key>Headers/SigmobLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			mGGQnDnE6mhVRKkyDdRLUR7NiaU=
			</data>
			<key>hash2</key>
			<data>
			LC3Tk+hBHkINEXDRGwcMRnKldAaifP9w8Uolqo5K/EM=
			</data>
		</dict>
		<key>Headers/SigmobProtoFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			jfvlI1dV1gIEYA7WhgwOiT3w4hg=
			</data>
			<key>hash2</key>
			<data>
			A5/FZr6X7lRomRgwK5LpIwGlxDh8VR9Baf10e+/UgOM=
			</data>
		</dict>
		<key>Headers/UIColor+SMColor.h</key>
		<dict>
			<key>hash</key>
			<data>
			tZJfIpfgXQIu5wRhvHI6JrppPW8=
			</data>
			<key>hash2</key>
			<data>
			D41qaSgKODkhP2vCT5bu1EXG978GrQez6lvBaELS7W8=
			</data>
		</dict>
		<key>Headers/UIImage+SMImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			FJ7hXDYaU7LcgatcZ/68eSoi9d8=
			</data>
			<key>hash2</key>
			<data>
			jFCYhtr+AgtAWAEJYmJ28dcQx8dxvXyDkIV7/6nNl3Q=
			</data>
		</dict>
		<key>Headers/UIImageView+SMWebCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			yx7sS65xVQNfT1EIxxOsHuRqc0I=
			</data>
			<key>hash2</key>
			<data>
			PCJkOgmzFxR2W5k+VAbSWMcik3Nnff08WOeDEQappSI=
			</data>
		</dict>
		<key>Headers/UIScrollView+WindPlayer.h</key>
		<dict>
			<key>hash</key>
			<data>
			HGymX/SxAEQZQm7Ba03vkI7bEQg=
			</data>
			<key>hash2</key>
			<data>
			Y5hu68URjZnBjrRITWxZ9GqnVE7AAkmAr3PfyNbhZvY=
			</data>
		</dict>
		<key>Headers/UIView+WindConverter.h</key>
		<dict>
			<key>hash</key>
			<data>
			YZWCP6fse30YtY9brQiyxSl9kIo=
			</data>
			<key>hash2</key>
			<data>
			M+bAEBP0SPe+yg6RVJ797OdTy/0a0hEDJQ6IIzLPLrc=
			</data>
		</dict>
		<key>Headers/UIView+WindViewTransition.h</key>
		<dict>
			<key>hash</key>
			<data>
			HWPslwr0nysTL85VPu5WlFpJ6tY=
			</data>
			<key>hash2</key>
			<data>
			P+2mnCPDZIAmI9f5amIArxwlxhDR8B0J6jM24I7K/oE=
			</data>
		</dict>
		<key>Headers/UIViewController+ZPTransition.h</key>
		<dict>
			<key>hash</key>
			<data>
			9s/1p0Tfk8KNmPmnatUfqadmos0=
			</data>
			<key>hash2</key>
			<data>
			v1H2b8yR+7ffROacFiWL8t7NAKDDC4SSGrtjBdCCmsY=
			</data>
		</dict>
		<key>Headers/UIViewController+ZPTransitionProperty.h</key>
		<dict>
			<key>hash</key>
			<data>
			yTxbtMDThGHq/61Mv1QaLSv1IaY=
			</data>
			<key>hash2</key>
			<data>
			+oOiek2/Wh1cpdsbchpy2LPh+1lmEBnvovwUA2pzaQo=
			</data>
		</dict>
		<key>Headers/View+SMSAdditions.h</key>
		<dict>
			<key>hash</key>
			<data>
			spt5Gygpm6PVJoMW/gPtK5KWPf8=
			</data>
			<key>hash2</key>
			<data>
			rhbAoRsPvrqZIYpbxJxnN2wZgOrmk3rVkZZmq7mY+u0=
			</data>
		</dict>
		<key>Headers/View+SMSShorthandAdditions.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZyHAN6XTwjJ1+c0OmzWs1FF380A=
			</data>
			<key>hash2</key>
			<data>
			slY5B3tJ9Yiql8IifTR0O9tCwaT9ThcvcdJOfgyxaDk=
			</data>
		</dict>
		<key>Headers/ViewController+SMSAdditions.h</key>
		<dict>
			<key>hash</key>
			<data>
			FinrL4BXNy+7RMOPzLxzCZJ/QZE=
			</data>
			<key>hash2</key>
			<data>
			DX5lFwiybx4j2t/iFZikg6KTEKrzyOH8c3xT0oJ8KGM=
			</data>
		</dict>
		<key>Headers/WFFCustomDevProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ps0AEa1QcaMpEOQfzD3RYOv162E=
			</data>
			<key>hash2</key>
			<data>
			4jBH0SlmPBkJW1blMOxvXQwt7//4/kxpRrTQJ6Q+sYE=
			</data>
		</dict>
		<key>Headers/WindAVPlayerManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			mKARQPthvQ5rNkCNriV/RGlwm6M=
			</data>
			<key>hash2</key>
			<data>
			XAu4jkWbQPS0iI/eJTuq6fJsIcquUDTxNdocn6Nf+yc=
			</data>
		</dict>
		<key>Headers/WindAsyncSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			86M11Qyz3utTwKQ4LjjeYWpgEEE=
			</data>
			<key>hash2</key>
			<data>
			VTk3/fvir7z6MtVu34Xpcoh2eYT9XijZ+DxY4f8rQbI=
			</data>
		</dict>
		<key>Headers/WindBaseLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			AnEVTxvayaHk4H0p/J3TZBJZB+0=
			</data>
			<key>hash2</key>
			<data>
			tKS65USq37OHF+dlDR1KvE8kBOslMzO+KQSE0P6cYXg=
			</data>
		</dict>
		<key>Headers/WindCocoaHTTPServer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ci0JwcX6MkFTupbNncWiRoGHnl8=
			</data>
			<key>hash2</key>
			<data>
			J6gAO8hkfrTQ9yrvK4YVNX/bV1ekbnsA1Avy3lqiJbA=
			</data>
		</dict>
		<key>Headers/WindCommonLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			FFymN7lLEVMUv55VQKDsCF4VINw=
			</data>
			<key>hash2</key>
			<data>
			fUcqN9dUrCbKv/WLneRJ7FD6iuY8lSSQL5aBAk4TNDc=
			</data>
		</dict>
		<key>Headers/WindCryptDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			srNzHkhsdxplEu7TMkFTGf2jOH0=
			</data>
			<key>hash2</key>
			<data>
			fRGNdAnqI00k7RbxP6Ji7vaE5kq8qwFo5ymMc/JQE3w=
			</data>
		</dict>
		<key>Headers/WindDDData.h</key>
		<dict>
			<key>hash</key>
			<data>
			8ag+z4+hL41xtFgglmA9oQUp2ec=
			</data>
			<key>hash2</key>
			<data>
			Er96jqZgAgY0W4eTz1AHZ14pKo65DJ9TvLPJ1LNCKE8=
			</data>
		</dict>
		<key>Headers/WindDDNumber.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xk2cJm3G/GaDQlG+t4hH8+BAqtE=
			</data>
			<key>hash2</key>
			<data>
			kjglLFVKNRcnZTxeBj96PKGrPHwp9CF8tWbEL9v4V/E=
			</data>
		</dict>
		<key>Headers/WindDDRange.h</key>
		<dict>
			<key>hash</key>
			<data>
			7WC0IM/30MF/zoGdVP9RhPAU3xU=
			</data>
			<key>hash2</key>
			<data>
			nIA4/UrmLAJCMH8Z37OKksmh8Jp8TZISp2oL/+Udr8g=
			</data>
		</dict>
		<key>Headers/WindDcLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			bUCWOhdarU18Kh+W/qAPueFkQMc=
			</data>
			<key>hash2</key>
			<data>
			uE9j7nOigZziUhaHxzKJWbTbgKg0w1E7UeEmNojF9VA=
			</data>
		</dict>
		<key>Headers/WindEnum.h</key>
		<dict>
			<key>hash</key>
			<data>
			5tyWj7MfQjXa1kQsVCC4Q2zufyI=
			</data>
			<key>hash2</key>
			<data>
			3E7sQJC+WZ8MDI+GzTnROZAZ3SG7uwGYhgLahT+V4hI=
			</data>
		</dict>
		<key>Headers/WindFloatView.h</key>
		<dict>
			<key>hash</key>
			<data>
			1JjLUL2u0B1XV+JBfbGO+q7NU5E=
			</data>
			<key>hash2</key>
			<data>
			7Z+1r9JtpsxCPgqCkSVrkjL7Q0DginshrN0hCoqxqYg=
			</data>
		</dict>
		<key>Headers/WindFoundation.h</key>
		<dict>
			<key>hash</key>
			<data>
			TBOGEttiwlTpLEd2BSP7NexWi1w=
			</data>
			<key>hash2</key>
			<data>
			A/ldNfqpaVN8xVTqtrX/bIJsBUvVLuETZ2pTam8Ziho=
			</data>
		</dict>
		<key>Headers/WindFoundationSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			uxaFEU7rwVDcCHnjj5KlhDFIwTQ=
			</data>
			<key>hash2</key>
			<data>
			WXlEAesJrRAKDGzgXaFHNgiOHIVD+uJXs5SxgAj0kME=
			</data>
		</dict>
		<key>Headers/WindGPBAny.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			2f6TVP2jBfTUNQS7421heq3coXw=
			</data>
			<key>hash2</key>
			<data>
			snqFdjsYRhVCJP0ViKbR5niYH4zcC1AnWTJgZRZHw9Q=
			</data>
		</dict>
		<key>Headers/WindGPBApi.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			z2pY5Mat1PXsrhQYvlxHHnwxnZA=
			</data>
			<key>hash2</key>
			<data>
			Fmrv27nRKUxSX34d+lCM0JUk8vC77FUJOgYySL8QXvs=
			</data>
		</dict>
		<key>Headers/WindGPBArray.h</key>
		<dict>
			<key>hash</key>
			<data>
			igk1PO3WFUQ/c0qRQsgkoVyRy2U=
			</data>
			<key>hash2</key>
			<data>
			L0OOJq7JVIuRhJ/cOmDCfzXviEBLyFuPbIaLdfqefpE=
			</data>
		</dict>
		<key>Headers/WindGPBArray_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			hyuEgDrR3QY2Eoaa7s2O5m4pi8g=
			</data>
			<key>hash2</key>
			<data>
			MP1zrV1QDKITrTopyfcBUVvHCc4aSgeZVMxJTpeAMog=
			</data>
		</dict>
		<key>Headers/WindGPBBootstrap.h</key>
		<dict>
			<key>hash</key>
			<data>
			EJtB0SsBXmS5g5kCrKGM8jOxri8=
			</data>
			<key>hash2</key>
			<data>
			NLeVY/gnW6DpOy0ps8AIhbKkpWuyLDpGcTSE3FThKlM=
			</data>
		</dict>
		<key>Headers/WindGPBCodedInputStream.h</key>
		<dict>
			<key>hash</key>
			<data>
			rgfOFigikezRun8svhdbLFOqz90=
			</data>
			<key>hash2</key>
			<data>
			TcRurtXTygyq6cenQZUQChJsS5U+vVVoumdvlwH5LTY=
			</data>
		</dict>
		<key>Headers/WindGPBCodedInputStream_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			PeJw4fVodvu9eoKYkHZaY4MnQkM=
			</data>
			<key>hash2</key>
			<data>
			QFUET5hpiIWjQq6hDc3N23lpm77rVx3g/LNCSVRYsX4=
			</data>
		</dict>
		<key>Headers/WindGPBCodedOutputStream.h</key>
		<dict>
			<key>hash</key>
			<data>
			BOOWI//v5BALyh3du/FAhgmnwbE=
			</data>
			<key>hash2</key>
			<data>
			I6dJaV5RsvCZIirXiztBAfti1SiHahrn9BjOPk3Qycc=
			</data>
		</dict>
		<key>Headers/WindGPBCodedOutputStream_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			cobSbb9Qcs8FllJlHHY0uhiB6ts=
			</data>
			<key>hash2</key>
			<data>
			aJ1ZwoxXlKVCkBRMUmYHxrO003bpbDIeEn2VAFDXFTQ=
			</data>
		</dict>
		<key>Headers/WindGPBDescriptor.h</key>
		<dict>
			<key>hash</key>
			<data>
			NW6KQLYi/nFnEcXk02NLKezfTBw=
			</data>
			<key>hash2</key>
			<data>
			opJ92gJMYSuzbArUTNKh/dU+VYGt2Rx0WOF+6RRDRp0=
			</data>
		</dict>
		<key>Headers/WindGPBDescriptor_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			ovGPZvULDHwJV0NnRxyQE+QE+0o=
			</data>
			<key>hash2</key>
			<data>
			8xJQNoPbuyok/bVUnbyY4qDMfQX1ySj+48/HZAl/o48=
			</data>
		</dict>
		<key>Headers/WindGPBDictionary.h</key>
		<dict>
			<key>hash</key>
			<data>
			81G9fRP90hmP3Rm0RiBuzYqBw68=
			</data>
			<key>hash2</key>
			<data>
			l44XbDdYdXcCGBqCNJRTLgWvzAkle6LfC3vbGYWMpZU=
			</data>
		</dict>
		<key>Headers/WindGPBDictionary_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			lODFZAxRJlB7dCRx2o6dkY6xxck=
			</data>
			<key>hash2</key>
			<data>
			c7dJBxejMjrh6Mwz9l+jirLsdMqB+2iqiTlb8/xm3Ew=
			</data>
		</dict>
		<key>Headers/WindGPBDuration.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			DvsY0zHtqj1pPgmeOJSx/wJ4zY0=
			</data>
			<key>hash2</key>
			<data>
			W1aJAAoEtbQ3yPb9CxpXsR/JV3hxmFczrtBRpw7NeEA=
			</data>
		</dict>
		<key>Headers/WindGPBEmpty.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			dN2GCB1LtGaWePfAGatAJewl6YE=
			</data>
			<key>hash2</key>
			<data>
			xZwSttf1S1XogJoD4TgN0TWZQn0lEnWoesq8BGqPrr0=
			</data>
		</dict>
		<key>Headers/WindGPBExtensionInternals.h</key>
		<dict>
			<key>hash</key>
			<data>
			/PnnpOWqO74EHxWiPe4co4a3e3k=
			</data>
			<key>hash2</key>
			<data>
			urhAwt7bEWQh1yVU79h6bfvcXP+ETE2fsB6Dn9X2VAs=
			</data>
		</dict>
		<key>Headers/WindGPBExtensionRegistry.h</key>
		<dict>
			<key>hash</key>
			<data>
			ajsJWaTVYnr7ZbVmeepjf5hUfM8=
			</data>
			<key>hash2</key>
			<data>
			xuMVL6UGlQ6YYie51075uZ6OX+tRUUPGZXXVIQ8NVH4=
			</data>
		</dict>
		<key>Headers/WindGPBFieldMask.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			NIOsxZtguaLhae/r9ZlF/b8epa4=
			</data>
			<key>hash2</key>
			<data>
			6RYeW8QMqcapeq1xUmyi7I3YH5g0y2gvaNAJb6v5XlY=
			</data>
		</dict>
		<key>Headers/WindGPBMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			wUGvrpQ7E4vgfZfsgR3XcSysZD0=
			</data>
			<key>hash2</key>
			<data>
			bMFJe+L+kkm0i0gTHWNYyDJgbe7MnYFJJzN5x0jM2xY=
			</data>
		</dict>
		<key>Headers/WindGPBMessage_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			WFQbTY2feybXydOkT7u1w8ZWWwQ=
			</data>
			<key>hash2</key>
			<data>
			apYTeRPy6EnbpT84cqiFtGoMn3UojokWtBEh7/zszug=
			</data>
		</dict>
		<key>Headers/WindGPBProtocolBuffers.h</key>
		<dict>
			<key>hash</key>
			<data>
			AqJbguedMQtt+f874NDcfbiZ0co=
			</data>
			<key>hash2</key>
			<data>
			F/wgwlodvvZYtOiJmkThrd4fojV6jDPhBq0AprS4t9M=
			</data>
		</dict>
		<key>Headers/WindGPBProtocolBuffers_RuntimeSupport.h</key>
		<dict>
			<key>hash</key>
			<data>
			sXsczE2aU06Qb54wtT0zAqlnN+U=
			</data>
			<key>hash2</key>
			<data>
			cweNjkYhAaloULRRqwJs5sUCl9LN/3tS+y+s0QFhjgg=
			</data>
		</dict>
		<key>Headers/WindGPBRootObject.h</key>
		<dict>
			<key>hash</key>
			<data>
			XXdjW0fMm0V9tYjLP6tnXfizRMQ=
			</data>
			<key>hash2</key>
			<data>
			JuICBaJ3lzMm8mLH0qiskMp/fuciZ94zG8j4ou0n/P4=
			</data>
		</dict>
		<key>Headers/WindGPBRootObject_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			W8ytbkI6ocbYmagFcY+ex7hVIvA=
			</data>
			<key>hash2</key>
			<data>
			ViPXCgbxefYCvmQvRYJoRuGpI83MPw7TK4/zxexKrXY=
			</data>
		</dict>
		<key>Headers/WindGPBRuntimeTypes.h</key>
		<dict>
			<key>hash</key>
			<data>
			9Aglwo0WqO9HLTwBbj9RdD8ql74=
			</data>
			<key>hash2</key>
			<data>
			xpCUQ2ORMwPIfP5980CCI/j544kuDydJpJ9lm6FxtbY=
			</data>
		</dict>
		<key>Headers/WindGPBSourceContext.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			Sk7gC36adFLBeSSgIuFPHmWO9Bc=
			</data>
			<key>hash2</key>
			<data>
			gowC+wlD6V4pe8ahCaE6ucWB3eDY+Wnt11IVIOALHQY=
			</data>
		</dict>
		<key>Headers/WindGPBStruct.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			vuqBhRtXCv7xgbaOzH5NGWSVXCI=
			</data>
			<key>hash2</key>
			<data>
			OG+L5+gBZRlkp54sz8O5OwOWAQWyYjz7nyV/nB7NcGw=
			</data>
		</dict>
		<key>Headers/WindGPBTimestamp.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			gbL6+uqkchD41rvhkjNlqBVzk6k=
			</data>
			<key>hash2</key>
			<data>
			wA9bd429ypMwjwj13wVsTo8nKTeAlt7L0G7dEzp0gOo=
			</data>
		</dict>
		<key>Headers/WindGPBType.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			B2+OJzcVoDunZlrc8z6Vo+Mc8nM=
			</data>
			<key>hash2</key>
			<data>
			d1JmWJwSjPmmp9/mnCvyt6CyZ8gk+WuFSBQb2WdeU/s=
			</data>
		</dict>
		<key>Headers/WindGPBUnknownField.h</key>
		<dict>
			<key>hash</key>
			<data>
			x3gQeujDE4xtNFNABDqJl+IaPIk=
			</data>
			<key>hash2</key>
			<data>
			apv+XRi/z/pgpWT3JCMlfCEKL0IytneCzvDluXDv8JE=
			</data>
		</dict>
		<key>Headers/WindGPBUnknownFieldSet.h</key>
		<dict>
			<key>hash</key>
			<data>
			G60Sl6lwIjbaT8Ks17kDZGU1wso=
			</data>
			<key>hash2</key>
			<data>
			0j66nUbn/3WY6SNPdrSArg0zc03dzirzocSAuuyvCpQ=
			</data>
		</dict>
		<key>Headers/WindGPBUnknownFieldSet_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			mRVkqT+KThQshuYrdfM+djWTsF8=
			</data>
			<key>hash2</key>
			<data>
			saVXOvGuCGd9oSNIvPSJxsZeyu+etTr/fQuS4FwcVnU=
			</data>
		</dict>
		<key>Headers/WindGPBUnknownField_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			jN6Qlv0S3zOsCgYjUTnGw9NxWTw=
			</data>
			<key>hash2</key>
			<data>
			W8fVFTu7dIOgpMSjz4K2lrhqq+1xzoLw9Xxn10TSq2I=
			</data>
		</dict>
		<key>Headers/WindGPBUtilities.h</key>
		<dict>
			<key>hash</key>
			<data>
			KnvXxD1OJ8zJs6GacX842MGC6SQ=
			</data>
			<key>hash2</key>
			<data>
			fUvyQkWII43Q8v/p7MjUNfqx91jFQ0kHGO6DP71qUpo=
			</data>
		</dict>
		<key>Headers/WindGPBUtilities_PackagePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			Z+zbkaTHa67F4xgPpuo61lMVImg=
			</data>
			<key>hash2</key>
			<data>
			b8EtsI/HKsChFkIqwGMwb3omlOaOt7DTbRHMuQv+YUI=
			</data>
		</dict>
		<key>Headers/WindGPBWellKnownTypes.h</key>
		<dict>
			<key>hash</key>
			<data>
			xSH5Qh1kZ/7iz48cHzdm2b5FHu8=
			</data>
			<key>hash2</key>
			<data>
			tNQDrklGUz+qTzR+1b4lwp3aiqrSEvVj/7iv8/JdiIs=
			</data>
		</dict>
		<key>Headers/WindGPBWireFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			10PVgk6a12azkMHoyuJlhcSCJM0=
			</data>
			<key>hash2</key>
			<data>
			nPN7l8Hdn1cLZxieAoEqVQFAwQjKuzZFLj15YVYRle8=
			</data>
		</dict>
		<key>Headers/WindGPBWrappers.pbobjc.h</key>
		<dict>
			<key>hash</key>
			<data>
			LmCzEb/Kaoip12CrThzDd7AcFWM=
			</data>
			<key>hash2</key>
			<data>
			llhlFyQFRUrFAD4AgbEtWUaaWJ4XmZjD2wjR2fL/xiM=
			</data>
		</dict>
		<key>Headers/WindHCDataCacheItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			Jq1zYNC3Ox+A2/WhngeVxr+MKUo=
			</data>
			<key>hash2</key>
			<data>
			sX5bxMeUybMq3R796a0bGmPFqv1ueGFHTzBthjoeoiQ=
			</data>
		</dict>
		<key>Headers/WindHCDataLoader.h</key>
		<dict>
			<key>hash</key>
			<data>
			78UXll75kfJ7O/497o1fRVlRTjw=
			</data>
			<key>hash2</key>
			<data>
			dEa8gUXCuV6WM5CHEI+oKIEiJSctjvn1UWw8ZoHsypM=
			</data>
		</dict>
		<key>Headers/WindHCDataReader.h</key>
		<dict>
			<key>hash</key>
			<data>
			i0+vAbXurDe2da41vfZpd5OvjV0=
			</data>
			<key>hash2</key>
			<data>
			6TcFBKY51+CbmO0uhRq/la0tC3UoU8rpgqitr9q+qr8=
			</data>
		</dict>
		<key>Headers/WindHCDataRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			Fj20mhuYW2UOHdT+qH3TtT1Zmpo=
			</data>
			<key>hash2</key>
			<data>
			p8/8ChVekG0ZIOwGosEJdlo6gsmwppIeE6mfpQHB4J4=
			</data>
		</dict>
		<key>Headers/WindHCDataResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			+KpJsHFw9bSuEWf4BRuCN1NoBBM=
			</data>
			<key>hash2</key>
			<data>
			7vZVmaoFgxqQuRtPDqRyQDvbA/CjHo57DIn+ah+SREs=
			</data>
		</dict>
		<key>Headers/WindHCHTTPConnection.h</key>
		<dict>
			<key>hash</key>
			<data>
			HWQXDYPm7x18gIyK2lqWRndMOaM=
			</data>
			<key>hash2</key>
			<data>
			1CFrUBnwM+YhIKTrJG2jJJlAsVEX1M3QtmasPj/PJoo=
			</data>
		</dict>
		<key>Headers/WindHCHTTPHeader.h</key>
		<dict>
			<key>hash</key>
			<data>
			Lriar2zw1fzZj4HVkmSeOUe0Acg=
			</data>
			<key>hash2</key>
			<data>
			vRLcU69+TUVV9wQs6SM4ezBlTqdPJbT3e0zx3NwcXJ8=
			</data>
		</dict>
		<key>Headers/WindHCHTTPResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			Mckio8V+pZ0Ckv8zVF6fMIP+Sxg=
			</data>
			<key>hash2</key>
			<data>
			7VJ1ZWPiEB8Kv8tntFaVv5zugu4gtvEhkcmHClPMl0s=
			</data>
		</dict>
		<key>Headers/WindHCHTTPServer.h</key>
		<dict>
			<key>hash</key>
			<data>
			xL7ZDkNGGQgqNM4xmqjWcFVK/0I=
			</data>
			<key>hash2</key>
			<data>
			Ge4sNDz01f9URh4ptKGNv/aqBSkQCNF1405iQrM4gOo=
			</data>
		</dict>
		<key>Headers/WindHCRange.h</key>
		<dict>
			<key>hash</key>
			<data>
			Sa/gBndC7nP5XAq8WSvmveQnEA0=
			</data>
			<key>hash2</key>
			<data>
			+V8EHho3Q4NZ5PFLn3J3Hr2Sy3aJHBWFMK19E0AgSTA=
			</data>
		</dict>
		<key>Headers/WindHTTPAsyncFileResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			P34UCKCW9iS3X16mZn7BT6OxOsU=
			</data>
			<key>hash2</key>
			<data>
			5V3K3dEgpCo805nsSrwN/Ctt5ibTwaWbFAz40ner8LY=
			</data>
		</dict>
		<key>Headers/WindHTTPAuthenticationRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			OMc5btqZdHbqTWW4HTyZZw++90c=
			</data>
			<key>hash2</key>
			<data>
			CCCVGHvoURzlRxyACHZjyrD3iS+yEyafMzUCLsuUN0A=
			</data>
		</dict>
		<key>Headers/WindHTTPCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			P8lYeV1ylFVO1IsP8yUvhYQbiQQ=
			</data>
			<key>hash2</key>
			<data>
			0sW583AgKFv6ztxG0YPkeb0jOmaeVMqdnJF5DITCRWY=
			</data>
		</dict>
		<key>Headers/WindHTTPConnection.h</key>
		<dict>
			<key>hash</key>
			<data>
			PILNAF2qbP1GHaL4Z9NX01b/84w=
			</data>
			<key>hash2</key>
			<data>
			LfyDLagbu9YFdFfewhSlJ8WBpYgY5PVW3yF44wLPVns=
			</data>
		</dict>
		<key>Headers/WindHTTPDataResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			o3EyZ9CgYdOy9x9LP4aXxVPXNtk=
			</data>
			<key>hash2</key>
			<data>
			VHo0I8EzmJop/rcYDDzcp3A6Y30DuGgt3pf9UiO1QU0=
			</data>
		</dict>
		<key>Headers/WindHTTPDynamicFileResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			nyqjGnaheRsBThdRs02boP+SVY8=
			</data>
			<key>hash2</key>
			<data>
			Jzy566tw3tq5wfmfpNEQ0EbvUjQ5J8fzLznTnUOXgrE=
			</data>
		</dict>
		<key>Headers/WindHTTPErrorResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			lW5AX6rbVx02j5PmILMWxtu6evA=
			</data>
			<key>hash2</key>
			<data>
			5fLmmRchcL3Dc9zFDkFCfgFMj5k9sUrkmlvLuWALX5A=
			</data>
		</dict>
		<key>Headers/WindHTTPFileResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			VnyCYYrTJOFrdHKapB1yFfYafA8=
			</data>
			<key>hash2</key>
			<data>
			3THHoXRAtOWQJh4Rra+q7wymGJ0kx7WEQkn9bDQ5ez4=
			</data>
		</dict>
		<key>Headers/WindHTTPLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			jPn4KVj8F3mcNJo/COGCeUnoIeE=
			</data>
			<key>hash2</key>
			<data>
			iwIMfap6ZzF/9QN//BArNcECdF4Ldf5nfTnMcFGwqeo=
			</data>
		</dict>
		<key>Headers/WindHTTPMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			GEEvEojQ8PR/IdhEgx6ogvv9RCw=
			</data>
			<key>hash2</key>
			<data>
			BBU1WFzDoquJR9kiC84gsmvirYugbUYGA7rSDS3r+ns=
			</data>
		</dict>
		<key>Headers/WindHTTPRedirectResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			1QaZWCrfTKMaBjZnb+Zf3ATnpKM=
			</data>
			<key>hash2</key>
			<data>
			zF73CDef5m9DD25tuB8hpJ9IF5s3lCalK/Ou0nEELCY=
			</data>
		</dict>
		<key>Headers/WindHTTPResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			ypH60m4qxlkjBhDmbXEZW3wZfXQ=
			</data>
			<key>hash2</key>
			<data>
			TuGB1XXIeW7jr+VAinVzCUZvIPJMOSRcLo3WJ9ayBmQ=
			</data>
		</dict>
		<key>Headers/WindHTTPServer.h</key>
		<dict>
			<key>hash</key>
			<data>
			TztHp7x472JuzqET1FKyA1E2BV8=
			</data>
			<key>hash2</key>
			<data>
			Izw9V+EnDOMN0mbkZjTXzAKv1YQRB5CNqQ3N1KEmkow=
			</data>
		</dict>
		<key>Headers/WindHooks.h</key>
		<dict>
			<key>hash</key>
			<data>
			hCbqrdFVuZCS5ARLHWky8bOVCyU=
			</data>
			<key>hash2</key>
			<data>
			nm9BlQcan9auGS2fQOc4wnGzCy9HUyPfUgRoTD7TkbM=
			</data>
		</dict>
		<key>Headers/WindKVOController.h</key>
		<dict>
			<key>hash</key>
			<data>
			nsiaHlGe1N5rDH3MI6OS3Hp0Sxs=
			</data>
			<key>hash2</key>
			<data>
			reTmoh7gu5SkkvXxK4xkc9vqTdIpQVTDepyz8nlEFvw=
			</data>
		</dict>
		<key>Headers/WindLandscapeViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			Qi9wVfkrzqi9l0kohDtiwnbChTY=
			</data>
			<key>hash2</key>
			<data>
			E8eTS2SCGY54ZG4THVXKWqrxtSDYmlRZ8xde8HX550Q=
			</data>
		</dict>
		<key>Headers/WindLandscapeWindow.h</key>
		<dict>
			<key>hash</key>
			<data>
			F2okvGZ46ipRaRPIvY3/Y9QgUJw=
			</data>
			<key>hash2</key>
			<data>
			SGQr/wBzKYZOVFSo9h6I35tQToVt29RdKvpJRJgl4W0=
			</data>
		</dict>
		<key>Headers/WindMultipartFormDataParser.h</key>
		<dict>
			<key>hash</key>
			<data>
			cdg+2pXe8wPk/C/P8W2UtBk1DzY=
			</data>
			<key>hash2</key>
			<data>
			YjSEE3gYZMz9eiCTGKEc86Q3FO0/0EC8GzhaZJ8cdHo=
			</data>
		</dict>
		<key>Headers/WindMultipartMessageHeader.h</key>
		<dict>
			<key>hash</key>
			<data>
			wos85bCazTtf0re9XuxwN9DSWH0=
			</data>
			<key>hash2</key>
			<data>
			EkNQgOuTyACYsCFcO6cjA2RIqSVYbjHukLK0SoLU5rA=
			</data>
		</dict>
		<key>Headers/WindMultipartMessageHeaderField.h</key>
		<dict>
			<key>hash</key>
			<data>
			cSUUIQRH1CC1Hpj9EvhdfmZTPVk=
			</data>
			<key>hash2</key>
			<data>
			xx1Y/mxdv3xWt7d7N/+xe8o7RnEUErW4sSB5hn7DERk=
			</data>
		</dict>
		<key>Headers/WindOrientationObserver.h</key>
		<dict>
			<key>hash</key>
			<data>
			pUn6cBxL8KM2vPL/Ykg1EQQRQXA=
			</data>
			<key>hash2</key>
			<data>
			VH30Fj8fNdd5ghWC5qdP5amGCNPwrEYi3sTPCub/X74=
			</data>
		</dict>
		<key>Headers/WindPersentInteractiveTransition.h</key>
		<dict>
			<key>hash</key>
			<data>
			jH+LxaLwP3wjP+mPWbi3PKOIejQ=
			</data>
			<key>hash2</key>
			<data>
			PfXFtwkZLj5IFxm4LK/33z55JimdxHfmG2a1safOsOs=
			</data>
		</dict>
		<key>Headers/WindPlayer.h</key>
		<dict>
			<key>hash</key>
			<data>
			mjunnpc5UCcjLTYsRplTtElPV+4=
			</data>
			<key>hash2</key>
			<data>
			A4lvGfjPwGjIdYIn+4mV6cB3TUsnPx0y2CedmR93KvA=
			</data>
		</dict>
		<key>Headers/WindPlayerConst.h</key>
		<dict>
			<key>hash</key>
			<data>
			Z7f/Yu11S9VfHe4YB01C8eiHJRA=
			</data>
			<key>hash2</key>
			<data>
			LyQmVtvNQSUqWl2D/LCKkAF0gZoh6E/4q+or5YWFb3U=
			</data>
		</dict>
		<key>Headers/WindPlayerController.h</key>
		<dict>
			<key>hash</key>
			<data>
			5R5SpPe7ILYLmOXOyXCXRrRWU30=
			</data>
			<key>hash2</key>
			<data>
			e2MxlXBlnPim0o/2vtfrJibVVVOMTiqBdaoDHZPmqtU=
			</data>
		</dict>
		<key>Headers/WindPlayerGestureControl.h</key>
		<dict>
			<key>hash</key>
			<data>
			Jaoi3i7hL6VYmluf9Jo8tBVKU8o=
			</data>
			<key>hash2</key>
			<data>
			x9aTrKUj6/fkrI1crZHyes0QvEreQwSnHau0UinBWbk=
			</data>
		</dict>
		<key>Headers/WindPlayerLogManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			rAjZCAu1wNWjrZLdAbj8HL2gkLw=
			</data>
			<key>hash2</key>
			<data>
			wRisCTh1Qbj6l3JFf+CnQLMXNjWNqy5CS8/XPXa+npM=
			</data>
		</dict>
		<key>Headers/WindPlayerMediaControl.h</key>
		<dict>
			<key>hash</key>
			<data>
			g325/WRvo+PDBsOOFE523DXNYr8=
			</data>
			<key>hash2</key>
			<data>
			IUoBOjhIfaR24wVLaLFqTOqdTZmJle9fjlEJxotVYlM=
			</data>
		</dict>
		<key>Headers/WindPlayerMediaPlayback.h</key>
		<dict>
			<key>hash</key>
			<data>
			a4u5BJ1JaedRvajFRMhfaeZN4VE=
			</data>
			<key>hash2</key>
			<data>
			8L42V4LRBeTP+xvGU/Day4rENDsHFzfr0oAaHhL0xO8=
			</data>
		</dict>
		<key>Headers/WindPlayerNotification.h</key>
		<dict>
			<key>hash</key>
			<data>
			KGsIx/s3RY1diXD6aFtYbgRbUbU=
			</data>
			<key>hash2</key>
			<data>
			nlLRknBpIBuZcqVQzxdQwtuLYaXl8qgKl1yTsxN9XlY=
			</data>
		</dict>
		<key>Headers/WindPlayerView.h</key>
		<dict>
			<key>hash</key>
			<data>
			CqjITfjuU00QUgRKuSZdbcPUVAg=
			</data>
			<key>hash2</key>
			<data>
			phUNhpnS93/S3CK9MvIyzCfi+YeIvsv6WdYZ6PxJbsk=
			</data>
		</dict>
		<key>Headers/WindPointer.h</key>
		<dict>
			<key>hash</key>
			<data>
			RXf5hXpWCfXzQeiKbItJCaisTBA=
			</data>
			<key>hash2</key>
			<data>
			DATZ48gPrEDfK6tE6OLloac2XFdmTTkIJkf09LU/WP4=
			</data>
		</dict>
		<key>Headers/WindPortraitViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			4XdE62BJtRQrOOxTBAwE7e4ICAQ=
			</data>
			<key>hash2</key>
			<data>
			Bnj14jnGnAs+25D6ku8x9/BJ4fmJgOWS3QQiZBxmSuM=
			</data>
		</dict>
		<key>Headers/WindPresentTransition.h</key>
		<dict>
			<key>hash</key>
			<data>
			XlN74itmUWXLyMsUXMbyuxJxt70=
			</data>
			<key>hash2</key>
			<data>
			HyRMlPq6aamOujPrx5nUoPC+iwe+seujmEWpDXZPcDk=
			</data>
		</dict>
		<key>Headers/WindReachabilityManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			WzAzjeVHWNkBmOktN5LehoXpCi4=
			</data>
			<key>hash2</key>
			<data>
			iyxI3tcJ6YmTWsqK1JY+F0WDKYl2KKSY6N5cR8AEn1I=
			</data>
		</dict>
		<key>Headers/WindSelectBuilder.h</key>
		<dict>
			<key>hash</key>
			<data>
			8rYIXE7nKDiTtl4Nmu4G2MMdb1E=
			</data>
			<key>hash2</key>
			<data>
			HSvuTZHKgLfKgeoX2qm1tvBvidoihZvVhClJt6dHPgc=
			</data>
		</dict>
		<key>Headers/WindShareData.h</key>
		<dict>
			<key>hash</key>
			<data>
			1Xn/erEz6aRNbBbLnHv+dRd4uE4=
			</data>
			<key>hash2</key>
			<data>
			C+lVE2orElAXrdwG7G1d6c+0O3Jtfr51G2s+iIKKOFI=
			</data>
		</dict>
		<key>Headers/WindSqlBuilder.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZwiYjCD9lKoCZqnNgRGmjuspcfE=
			</data>
			<key>hash2</key>
			<data>
			Aa18+cz7LmcJp9MhzOyccDnUo+xsILkOPYnKmwK8NOU=
			</data>
		</dict>
		<key>Headers/WindStorageManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			l7gB65MqJ2Z441w8IZ0kygdirbM=
			</data>
			<key>hash2</key>
			<data>
			hk9X4p15kUDLDKoKkJAhieQtVQgg4cU0WH876itMjJ4=
			</data>
		</dict>
		<key>Headers/WindTableBuilder.h</key>
		<dict>
			<key>hash</key>
			<data>
			G89SdSImpqtTd4dwTR6hgsnqzCc=
			</data>
			<key>hash2</key>
			<data>
			Ne7XHJ/ycTlcSorecG/xOXifXHqLfLNvpgQpzHjhvrE=
			</data>
		</dict>
		<key>Headers/WindWKProcessPool.h</key>
		<dict>
			<key>hash</key>
			<data>
			a6L5LwbxS0YVDlzSTbFNJZ8v+zI=
			</data>
			<key>hash2</key>
			<data>
			KnxieRTHXLu2P6A/igryrB4Zg4mS/xdmxLXay+2hWQk=
			</data>
		</dict>
		<key>Headers/WindWKWebViewJavascriptBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			Y7ssTeUoIDj5PVkDoO6VacYwsT4=
			</data>
			<key>hash2</key>
			<data>
			mZo3+ZBtj7n0EbxbrcFR6hyFS3PvD3fiK45g4m6RuxA=
			</data>
		</dict>
		<key>Headers/WindWebSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			0UkCKwr2eoaRtFM8cDc/KaDgpK8=
			</data>
			<key>hash2</key>
			<data>
			XLeTPlKsZfQYC4pFtpvAU2vCgEVHvmFXRdxm8J2teyY=
			</data>
		</dict>
		<key>Headers/WindWebView.h</key>
		<dict>
			<key>hash</key>
			<data>
			EV99eDTG75xwOuH2ipb6UkhupCE=
			</data>
			<key>hash2</key>
			<data>
			aOuKebvC0HnJWhohqpXVon57CPBgNlfrjnDbwBo5IXY=
			</data>
		</dict>
		<key>Headers/WindWebViewJavascriptBridgeBase.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xt8J1+if3cG7Hww8gddHryc2gJI=
			</data>
			<key>hash2</key>
			<data>
			fLuebpGZ93mY+HUaXQ9vwYavNFE7d4sFnsDh4UyOTIM=
			</data>
		</dict>
		<key>Headers/WindWebViewJavascriptBridge_JS.h</key>
		<dict>
			<key>hash</key>
			<data>
			rrMAMGUcjJUz1XYYA4nWYGcJ+mE=
			</data>
			<key>hash2</key>
			<data>
			I/oE1Po2HsX/G19jmxXp7tzvEA4eQN+GWa10fuQ7cmA=
			</data>
		</dict>
		<key>Headers/WindWhereBuilder.h</key>
		<dict>
			<key>hash</key>
			<data>
			pBOTkoxHWIkuZPnN0H1U+GOgjnA=
			</data>
			<key>hash2</key>
			<data>
			8IfYQ92rEkGkbXfT5Ac53wWyk+LzWTqzvzMT3cjSU6Q=
			</data>
		</dict>
		<key>Headers/WindmillAnimationTool.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ds0hepDEC8hHu7heU4HeqixwN3U=
			</data>
			<key>hash2</key>
			<data>
			6qW5K6MH1fyvjgMknN3//LgdvphAgPCJDfHcpNudqm8=
			</data>
		</dict>
		<key>Headers/ZPTransition.h</key>
		<dict>
			<key>hash</key>
			<data>
			81KdJ36DoKmVpxSxe9ohE3x0WaI=
			</data>
			<key>hash2</key>
			<data>
			vRP0kvM5Dk9L7HgR8fFavLpTTyMUUhqcgr4Fs2tlNGU=
			</data>
		</dict>
		<key>Headers/ZPTransitionManager+ViewMoveAnimation.h</key>
		<dict>
			<key>hash</key>
			<data>
			GDleanS9Zcd9kP334TohlwGUWRQ=
			</data>
			<key>hash2</key>
			<data>
			6XS4PT3klnGeCcNCA0fdAGWYjtQUEB74p4u+IMqPglM=
			</data>
		</dict>
		<key>Headers/ZPTransitionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			r88Cr635ODWi7uJel4vcK+cr8IA=
			</data>
			<key>hash2</key>
			<data>
			+QsLSfWp/hF4hzyeB/f1SmVPYAF6GqRAZsyJ8sdaeMk=
			</data>
		</dict>
		<key>Headers/ZPTransitionProperty.h</key>
		<dict>
			<key>hash</key>
			<data>
			Fv3l0vRuXQFjukQB6SdG3DsLldo=
			</data>
			<key>hash2</key>
			<data>
			BISWnkuAR6Fr+nKsyt5mkgb5LFnSzbXaBPzfA13bkBI=
			</data>
		</dict>
		<key>Headers/ZPTypedefConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			5lbGJEKBJnRbUW6zlDoHsaKMWKU=
			</data>
			<key>hash2</key>
			<data>
			iDezkqMUQCVX4ShiutKgeJLje6WTsmsEx3ztTZTBWNg=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			5zBoJbc6nPg6Msf6u42CI6z1i6U=
			</data>
			<key>hash2</key>
			<data>
			alfod7zLXzeyzGOQ1QHRWCqtBPHxWT2b0Kf42lpMqSE=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			/Ob96ulOlVP7Gztxfa9S4AcuRpg=
			</data>
			<key>hash2</key>
			<data>
			oPtiJ2YUNtx2Me7YEA5viC6ldsJ8TnjzTR6T7WjZ5mc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
