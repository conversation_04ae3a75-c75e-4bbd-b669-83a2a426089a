//
//  C<PERSON>_ShanYanSDK.h
//  C<PERSON>_ShanYanSDK
//
//  Created by wang<PERSON><PERSON> on 2018/10/29.
//  Copyright © 2018 wanglijun. All rights reserved.
//

#import <UIKit/UIKit.h>

//! Project version number for CL_ShanYanSDK.
FOUNDATION_EXPORT double CL_ShanYanSDKVersionNumber;

//! Project version string for CL_ShanYanSDK.
FOUNDATION_EXPORT const unsigned char CL_ShanYanSDKVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <CL_ShanYanSDK/PublicHeader.h>
#import <CL_ShanYanSDK/CLShanYanSDKManager.h>
#import <CL_ShanYanSDK/CLCompleteResult.h>
#import <CL_ShanYanSDK/CLFaceIDTool.h>
