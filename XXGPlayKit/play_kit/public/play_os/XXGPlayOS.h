//
//  XXGPlay.h
//  XXGOSPlayKit
//
//  Created by apple on 2025/2/27.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
@protocol XXGPlayDelegate;

NS_ASSUME_NONNULL_BEGIN

@interface XXGPlayOS : NSObject
// MARK: - 基础必接
/// 设置代理
+ (void)xxpk_setPlayDelegate:(id<XXGPlayDelegate>)delegate;

/// 登录
+ (void)xxpk_comein;

/// 退出
+ (void)xxpk_logout;

/// 下单
+ (void)xxpk_createOrder:(NSString *)xxpk_cpOrderId
        xxpk_productCode:(NSString *)xxpk_productCode
             xxpk_amount:(NSString *)xxpk_amount
        xxpk_productName:(NSString *)xxpk_productName
           xxpk_serverId:(NSString *)xxpk_serverId
          xxpk_extraInfo:(NSString *)xxpk_extraInfo
             xxpk_roleId:(NSString *)xxpk_roleId
           xxpk_roleName:(NSString *)xxpk_roleName
          xxpk_roleLevel:(NSString *)xxpk_roleLevel;

/// 上报角色
+ (void)xxpk_uploadRoleInfo:(NSString * _Nonnull)xxpk_serverId
            xxpk_serverName:(NSString * _Nonnull)xxpk_serverName
                xxpk_roleId:(NSString * _Nonnull)xxpk_roleId
              xxpk_roleName:(NSString * _Nonnull)xxpk_roleName
             xxpk_roleLevel:(NSString * _Nonnull)xxpk_roleLevel
                xxpk_extend:(NSDictionary * _Nullable)xxpk_extend;

/// 启动
+ (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *_Nullable)launchOptions xconnectOptions:(UISceneConnectionOptions *_Nullable)connectionOptions;

/// 通用链接
+ (BOOL)xxpk_applicationOpenURL:(NSURL *_Nullable)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *_Nullable)options xURLContexts:(NSSet<UIOpenURLContext *> *_Nullable)URLContexts;

// MARK: - 扩展可选
/// 打开个人中心
+ (void)xxpk_openUserCenterSidebar:(NSString *)type;

/// 内购修复
+ (void)xxpk_iapRepair;

@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindFacebook;
@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindVK;

/// Facebook - 分享-url
+ (void)xxpk_facebookShareWithUrl:(NSString *)url;

/// Facebook - 分享-image
+ (void)xxpk_facebookShareWithImgUrl:(NSString *)imgUrl;

/// vk - 关注
+ (void)xxpk_vkPage;

/// Facebook - 关注
+ (void)xxpk_facebookPage;

/// Facebook -邀请好友一起游戏
+ (void)xxpk_facebookInvite;

/// Facebook - 绑定
+ (void)xxpk_facebookBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;

/// VK - 绑定
+ (void)xxpk_VKBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;

// MARK: - 打点
+ (void)xxpk_logFacebookEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAppFlyerEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logFirebaseEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAdjustEvent:(NSString *)event params:(NSDictionary *_Nullable)params;

// MARK: - AppLovin
+ (void)xxpk_showRewardedAdForCustomData:(nullable NSString *)customData complate:(void(^)(BOOL result))complate;

// MARK: - 上报自定义日志
+ (void)xxpk_reportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content;

+ (void)xxpk_translateOriginalLanguage:(NSString *)originalLanguage targetLanguage:(NSString *)targetLanguage toBeTranslated:(NSString *)toBeTranslated complate:(void(^)(NSString *translated, NSString *_Nullable error))complate;

@end

NS_ASSUME_NONNULL_END
