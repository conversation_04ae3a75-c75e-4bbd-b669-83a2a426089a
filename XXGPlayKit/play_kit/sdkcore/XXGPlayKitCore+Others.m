//
//  XXGPlayKitCore+Other.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/24.
//

#import "XXGPlayKitCore+Others.h"
#import "XXGWindowManager.h"
#import "XXGUIKit.h"
#import "XXGPlayKitConfig.h"
#import "XXGAlertView.h"
#import "XXGBoxManager.h"
#import "XXGNetworkList.h"
#import "XXGThirdManager.h"

#ifdef XXGPLAYKITCN_TARGET
#import "XXGSigmobManager.h"
#endif

#ifdef XXGPLAYKITOS_TARGET
    #import "XXGFacebookManager.h"
    #import "XXGVKManager.h"
    #import "XXGAppLovinManager.h"
#endif

@implementation XXGPlayKitCore (Others)

#ifdef XXGPLAYKITCN_TARGET

- (void)xxpk_sigmobShowAdComplate:(void(^)(BOOL result))complate {
    [XXGSigmobManager xxpk_sigmobShowAdFromRootViewController:[XXGWindowManager shared].xxpk_currentWindow.rootViewController complate:complate];
}

#endif

#ifdef XXGPLAYKITOS_TARGET

+ (BOOL)xxpk_isBindFacebook {
    return [XXGBoxManager xxpk_comeinedBox].xxpk_fbBind;
}

+ (BOOL)xxpk_isBindVK{
    return [XXGBoxManager xxpk_comeinedBox].xxpk_vkBind;
}

+ (void)xxpk_facebookShareWithUrl:(NSString *)url {
    [XXGFacebookManager xxpk_sharedLinkToFacebookWithUrl:url withvc:[XXGUIKit xxpk_currentWindow].rootViewController];
}

+ (void)xxpk_facebookShareWithImgUrl:(NSString *)imgUrl {
    [XXGFacebookManager xxpk_sharedImageToFacebookWithImageUrl:imgUrl withvc:[XXGUIKit xxpk_currentWindow].rootViewController];
}

+ (void)xxpk_vkPage {
    [XXGVKManager xxpk_jumpToVKAndFollw:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_vk_home];
}

+ (void)xxpk_facebookPage {
    [XXGFacebookManager xxpk_jumpToFacebookAndFollw:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_fb_home];
}

// 邀请好友
+ (void)xxpk_facebookInvite {
    [XXGFacebookManager xxpk_launchFriendFinderDialogWithCompletionHandler:^(BOOL success, NSError * _Nullable error) {
        if (error) {
            [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:error.localizedDescription completion:nil];
        }
    }];
}

+ (void)xxpk_facebookBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler {
    
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish) {
        handler(nil, __string_core.xxpk_comeinError);
        return;
    }
    if (self.xxpk_isBindFacebook) {
        handler(nil, __string_core.xxpk_isbinded);
        return;
    }
    
    [XXGFacebookManager xxpk_oauth:[XXGUIKit xxpk_currentWindow].rootViewController handler:^(NSString * _Nonnull userID, NSString * _Nonnull name, NSString * _Nonnull token, NSString * _Nonnull auth_token, NSString * _Nonnull nonce, NSError * _Nonnull error, BOOL isCancelled) {
        
        if (isCancelled) {
            if (handler) {
                handler(nil, __string_core.xxpk_cancel);
            }
        }else if (error) {
            if (handler) {
                handler(nil,error.localizedDescription);
            }
        }else {
            [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkBindFacebookWithUid:userID uToken:token authToken:auth_token nonce:nonce success:^(NSDictionary * _Nonnull responseObject) {
                if (handler) {
                    handler([XXGBoxManager xxpk_comeinedBoxJson],nil);
                }
            } failure:^(NSError * _Nonnull error) {
                if (handler) {
                    NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
                    handler(nil,errorDescrip);
                }
            }];
        }
    }];
}

+ (void)xxpk_VKBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler {
    
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish) {
        handler(nil, __string_core.xxpk_comeinError);
        return;
    }
    if (self.xxpk_isBindVK) {
        handler(nil, __string_core.xxpk_isbinded);
        return;
    }
    
    [XXGVKManager xxpk_oauthOnViewController:[XXGUIKit xxpk_currentWindow].rootViewController handler:^(BOOL isCancell, NSString * _Nonnull userID, NSString * _Nonnull token, NSString * _Nonnull error) {
        if (isCancell) {
            if (handler) {
                handler(nil, __string_core.xxpk_cancel);
            }
        }else if (error && error.length > 0) {
            if (handler) {
                handler(nil,error);
            }
        }else {
            [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkBindVKWithUid:userID uToken:token success:^(NSDictionary * _Nonnull responseObject) {
                if (handler) {
                    handler([XXGBoxManager xxpk_comeinedBoxJson],nil);
                }
            } failure:^(NSError * _Nonnull error) {
                if (handler) {
                    NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
                    handler(nil,errorDescrip);
                }
            }];
        }
    }];
}

+ (void)xxpk_logFacebookEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    [XXGThirdManager xxpk_logFacebookEvent:event params:params];
}
+ (void)xxpk_logAppFlyerEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    [XXGThirdManager xxpk_logAppFlyerEvent:event params:params];
}
+ (void)xxpk_logFirebaseEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    [XXGThirdManager xxpk_logFirebaseEvent:event params:params];
}
+ (void)xxpk_logAdjustEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    [XXGThirdManager xxpk_logAdjustEvent:event params:params];
}

// MARK: - AppLovin
+ (void)xxpk_showRewardedAdForCustomData:(nullable NSString *)customData complate:(void(^)(BOOL result))complate {
    NSDictionary *customDataDic = @{__data_core.xxpk_uid:[XXGBoxManager xxpk_comeinedBox].xxpk_boxId?:@"",
                                    __data_core.xxpk_cp_extra:customData?:@""};
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:customDataDic options:0 error:&error];
    NSString *customDataJsonString = @"";
    if (jsonData) {
        customDataJsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    } else {
        NSLog(@"JSON 转换失败: %@", error.localizedDescription);
    }
    
    [XXGAppLovinManager xxpk_showRewardedAdForCustomData:customDataJsonString complate:complate];
}

// MARK: - 上报日志
- (void)xxpk_reportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkReportlogWithType:xxpk_type xxpk_content:xxpk_content];
}

- (void)xxpk_translateOriginalLanguage:(NSString *)originalLanguage targetLanguage:(NSString *)targetLanguage toBeTranslated:(NSString *)toBeTranslated complate:(void(^)(NSString *translated, NSString *_Nullable error))complate  {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkTranslateOriginalLanguage:originalLanguage targetLanguage:targetLanguage textList:@[toBeTranslated] success:^(NSDictionary * _Nonnull responseObject) {
        complate(responseObject[__data_core.xxpk_data][0],nil);
    } failure:^(NSError * _Nonnull error) {
        complate(@"",error.localizedDescription);
    }];
}
#endif

@end
