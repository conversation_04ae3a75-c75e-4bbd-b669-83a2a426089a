//
//  XXGPlayKitCore+Other.h
//  XXGPlayKit
//
//  Created by apple on 2025/3/24.
//

#import "XXGPlayKitCore.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGPlayKitCore (Others)

#ifdef XXGPLAYKITCN_TARGET

- (void)xxpk_sigmobShowAdComplate:(void(^)(BOOL result))complate;

#endif

#ifdef XXGPLAYKITOS_TARGET

@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindFacebook;
@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindVK;

+ (void)xxpk_facebookShareWithUrl:(NSString *)url;

+ (void)xxpk_facebookShareWithImgUrl:(NSString *)imgUrl;

+ (void)xxpk_vkPage;

+ (void)xxpk_facebookPage;

+ (void)xxpk_facebookInvite;

+ (void)xxpk_facebookBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;

+ (void)xxpk_VKBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;

+ (void)xxpk_logFacebookEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAppFlyerEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logFirebaseEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAdjustEvent:(NSString *)event params:(NSDictionary *_Nullable)params;

// MARK: - AppLovin
+ (void)xxpk_showRewardedAdForCustomData:(nullable NSString *)customData complate:(void(^)(BOOL result))complate;

// MARK: - 上报日志
- (void)xxpk_reportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content;

- (void)xxpk_translateOriginalLanguage:(NSString *)originalLanguage targetLanguage:(NSString *)targetLanguage toBeTranslated:(NSString *)toBeTranslated complate:(void(^)(NSString *translated, NSString *_Nullable error))complate ;
#endif

@end

NS_ASSUME_NONNULL_END
