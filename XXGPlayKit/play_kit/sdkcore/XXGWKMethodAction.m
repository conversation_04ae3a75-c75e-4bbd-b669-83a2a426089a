//
//  XXGWKMethodAction.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/24.
//

#import "XXGWKMethodAction.h"
#import "XXGPlayKitConfig.h"
#import "NSString+XXGString.h"
#import "XXGUIKit.h"
#import "XXGPlayKitCore.h"
#import "XXGAlertView.h"
#import "XXGBoxManager.h"
#import "NSString+URLEncoding.h"
#import "XXGIAPManager.h"
#import "NSObject+XXGModel.h"
#import "XXGNetworkList.h"
#import "XXGToast.h"
#import "NSURL+XXGAnalyse.h"
#import "XXGPlayKitCore+Others.h"
#import "XXGProductBody.h"
#import "ZBObjectiveCBeaver.h"
#import "XXGWindowManager.h"
#import <Photos/Photos.h>

#ifdef XXGPLAYKITCN_TARGET
    #import "XXGWechatOpenSDKManager.h"
#endif


@implementation XXGWKMethodAction

+ (void)xxpk_wkView:(WKWebView *)wkView makeMethodAction:(NSString *)method arg:(id)arg {
    ZBLogInfo(@"WebView事件-%@",method);
    if (method.xxpk_isEmpty) {
        return;
    }
    if ([method isEqualToString:__data_core.xxpk_core_mt_changep]) { // changePassword
        [XXGPlayKitCore.shared xxpk_showUIofChangeBoxKey:wkView];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_bindm]) {// bindMobile
        [XXGPlayKitCore.shared xxpk_showUIofbindMobile:@(NO) hasWkView:wkView];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_switcha]) {// switchAccount
        [self __xxpk_wk_switchAccount];
    }else if ([method isEqualToString:__data_core.xxpk_recomein]) {// relogin
        [XXGPlayKitCore.shared xxpk_logout];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_openURL]) {// openURL
        [self __xxpk_openUrl:arg];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_ucenter]) {// ucenter
        [self __xxpk_wk_ucenter:arg];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_iapRepair]) {// iapRepair
        [self __xxpk_wk_iapRepair:arg];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_getInfomation]) {// getInfomation
        [self __xxpk_wk_getInfomation:wkView];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_accountRemove]) {// accountRemove
        [self __xxpk_removeAccount];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_getApiUrl]) {// getApiUrl
        [self __xxpk_getApiUrl:wkView];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_getToken]) {// gettoken
        [self __xxpk_getToken:wkView];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_popup]) {// popup
        [self __xxpk_popup:arg];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_capture_screen]) {// captureScreen
        [self __xxpk_captureScreen:wkView];
    }
    
    // 关闭当前弹窗
    else if ([method isEqualToString:__data_core.xxpk_core_mt_userInfoSub]||
              [method isEqualToString:__data_core.xxpk_core_mt_closeSplash]) { //userInfoSub & closeSplash
        [XXGUIKit xxpk_dissmissCurrentWinow];
    }
    
    // arg String 类型 调用的是协WK议方法
    else if([method isEqualToString:__data_core.xxpk_core_mt_openUserCenterSidebar]) {//openUserCenterSidebar
        [XXGPlayKitCore.shared xxpk_openUserCenterSidebar:arg];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_coin_p]) {//coinP
        [self __xxpk_coinP:arg];
    }
    
#ifdef XXGPLAYKITCN_TARGET
    else if([method isEqualToString:__data_core.xxpk_core_mt_wxbind]) {// wxLogin 微信绑定
        isUcenterBind = YES;
        [XXGPlayKitCore.shared xxpk_openWeiXinAuth];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_wx_confirm_pay]) {// wxConfirmPay 微信确认收款
        [self __xxpk_wx_confirm_pay:arg];
    }
#endif
    
#ifdef XXGPLAYKITOS_TARGET
    else if([method isEqualToString:__data_core.xxpk_core_mt_facebookShare]) {
        [self __xxpk_facebookShare:arg];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_facebookSub]) {
        [self __xxpk_facebookSub];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_facebookBind]) {
        [self __xxpk_facebookBind];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_facebookInvite]) {
        [self __xxpk_facebookInvite];
    }
#endif
}

// WKScriptMessageHandler
+ (void)__xxpk_coinP:(NSString *)json {
    NSData *jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:nil];
    if (!dic) {
        return;
    }
    XXGProductBody *body = [XXGProductBody xxpk_modelWithDict:dic];
    [XXGPlayKitCore.shared xxpk_creatOrder:body xxpk_isCoinOrder:YES];
}

+ (void)__xxpk_popup:(NSURL *)url {
    NSDictionary *ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    if ([ext[__data_core.xxpk_action] isEqualToString:__data_core.xxpk_open]) {
        [[XXGPlayKitCore shared] xxpk_showUIofPopup:ext];
    }else {
        [XXGUIKit xxpk_dissmissCurrentWinow];
    }
}

+ (void)__xxpk_getToken:(WKWebView *)vkview {
    NSString * jsFunc = [NSString stringWithFormat:__data_core.xxpk_core_mt_func_getToken,[XXGBoxManager xxpk_comeinedBox].xxpk_boxToken].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError * _Nullable error) {
        NSLog(@"%@----%@",resultValue, error);
    }];
}

+ (void)__xxpk_getApiUrl:(WKWebView *)vkview {
    NSString * jsFunc = [NSString stringWithFormat:__data_core.xxpk_core_mt_func_getApiUrl,XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError * _Nullable error) {
        NSLog(@"%@----%@",resultValue, error);
    }];
}

+ (void)__xxpk_removeAccount {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkRemoveAccount:^(NSDictionary * _Nonnull responseObject) {
        [XXGPlayKitCore.shared xxpk_logout];
        [XXGToast showBottom:__string_core.xxpk_account_removed];
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorDescrip completion:nil];
    }];
}

+ (void)__xxpk_openUrl:(NSURL *)url {
    NSDictionary * ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    [XXGPlayKitCore.shared xxpk_coreHandleOpenUrl:ext[__data_core.xxpk_url]];
}

// 打开用户中心
+ (void)__xxpk_wk_ucenter:(NSURL *)url {
    
    NSString *query = url.query;
    
    if (query.xxpk_isNotEmpty && query.length > 4) {
        query = [query substringFromIndex:4]; // 取url= 后面字符
        [XXGUIKit xxpk_dissmissAllWindows];
        [XXGPlayKitCore.shared xxpk_showUIofUCenter:query.xxpk_urlDecodedString];
    }else {
        [XXGUIKit xxpk_dissmissAllWindows];
        [XXGPlayKitCore.shared xxpk_showUIofUCenter:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url];
    }
}

/// 内购修复
+ (void)__xxpk_wk_iapRepair:(NSURL *)url {
    [XXGPlayKitCore.shared xxpk_iapRepair];
}

+ (void)__xxpk_wk_getInfomation:(WKWebView *)vkview {
    NSMutableDictionary *dic = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    XXGBoxContent *box = [XXGBoxManager xxpk_comeinedBox];
    NSMutableDictionary *udic = [NSMutableDictionary new];
    udic[__data_core.xxpk_core_mt_getInfomation_uid] = box.xxpk_boxId;
    udic[__data_core.xxpk_core_mt_getInfomation_name] = box.xxpk_boxName;
    udic[__data_core.xxpk_core_mt_getInfomation_token] = box.xxpk_boxToken;
#ifdef XXGPLAYKITOS_TARGET
    udic[__data_core.xxpk_core_mt_getInfomation_fbuid] = box.xxpk_facebookUid;
    udic[__data_core.xxpk_core_mt_getInfomation_fbtoken] = box.xxpk_facebookToken;
    udic[__data_core.xxpk_core_mt_getInfomation_fbauthtoken] = box.xxpk_facebookAuthToken;
    udic[__data_core.xxpk_core_mt_getInfomation_fbnonce] = box.xxpk_facebookNonce;
#endif
    dic[__data_core.xxpk_core_mt_getInfomation_user] = udic;
    NSData *data = [NSJSONSerialization dataWithJSONObject:dic options:kNilOptions error:nil];
    NSString *string = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString * jsFunc = [NSString stringWithFormat:__data_core.xxpk_core_mt_func_getInfomation,string].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"returnInfomation %@----%@",result, error);
    }];
}

+ (void)__xxpk_wk_switchAccount {
    [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:__string_core.xxpk_switchBox buttonTitles:@[__string_core.xxpk_ok,__string_core.xxpk_cancel] completion:^(NSInteger buttonIndex) {
        if (buttonIndex == 0) {
            [XXGPlayKitCore.shared xxpk_logout];
        }
    }];
}

+ (void)__xxpk_captureScreen:(WKWebView *)targetView {
    UIGraphicsBeginImageContextWithOptions(targetView.bounds.size, NO, 0.0);
    [targetView.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
        [PHAssetChangeRequest creationRequestForAssetFromImage:image];
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        NSString * jsFunc = [NSString stringWithFormat:__data_core.xxpk_core_mt_func_captureScreenCallback,@(success)].mutableCopy;
        [targetView evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError * _Nullable error) {
            NSLog(@"%@----%@",resultValue, error);
        }];
    }];
    
}

#ifdef XXGPLAYKITCN_TARGET

+ (void)__xxpk_wx_confirm_pay:(NSURL *)url {
    NSDictionary *ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    
    NSString *xxpk_query = [ext[@"query"] xxpk_urlDecodedString];
    NSLog(@"query = %@", xxpk_query);

    NSArray *pairs = [xxpk_query componentsSeparatedByString:@"&"];
    NSMutableDictionary *ParaDict = [NSMutableDictionary new];

    for (NSString *pair in pairs) {
        NSRange eqRange = [pair rangeOfString:@"="];
        if (eqRange.location != NSNotFound) {
            NSString *key = [pair substringToIndex:eqRange.location];
            NSString *value = [pair substringFromIndex:eqRange.location + 1];
            // 对 value 再次解码（若需要）
            NSString *decodedValue = [value xxpk_urlDecodedString];
            if (key.length > 0 && decodedValue) {
                ParaDict[key] = decodedValue;
            }
        }
    }

    // 之后取值
    NSString *xxpk_mchId = ParaDict[@"mchId"];
    NSString *xxpk_appid = ParaDict[@"appId"];
    NSString *xxpk_package = ParaDict[@"package"];
    
    NSString *xxpk_req_query = [NSString stringWithFormat:@"mchId=%@&appId=%@&package=%@",xxpk_mchId.xxpk_urlEncodedString,xxpk_appid,xxpk_package.xxpk_urlEncodedString];
    NSLog(@"xxpk_req_query = %@", xxpk_req_query);
    
    [XXGWechatOpenSDKManager xxpk_registerApp:xxpk_appid universalLink:@"https://xxbox.cn/"];
    [XXGWechatOpenSDKManager xxpk_sendReqWithQuery:xxpk_req_query];
}

#endif

#ifdef XXGPLAYKITOS_TARGET
+ (void)__xxpk_facebookShare:(NSURL *)url {
    NSDictionary * ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    NSString *linkUrl = ext[__data_core.xxpk_url];
    NSString *imgUrl = ext[__data_core.xxpk_imgUrl];
    if (linkUrl.xxpk_isNotEmpty) {
        [XXGPlayKitCore xxpk_facebookShareWithUrl:linkUrl];
        return;
    }
    if (imgUrl.xxpk_isNotEmpty) {
        [XXGPlayKitCore xxpk_facebookShareWithImgUrl:imgUrl];
        return;
    }
}

+ (void)__xxpk_facebookSub {
    [XXGPlayKitCore xxpk_facebookPage];
}

+ (void)__xxpk_facebookBind {
    [XXGPlayKitCore xxpk_facebookBind:^(NSDictionary * _Nullable userInfo, NSString * _Nonnull errorMsg) {
        if (errorMsg) {
            [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorMsg completion:nil];
        }else {
            [XXGToast showBottom:__string_core.xxpk_bind_sus];
        }
    }];
}

+ (void)__xxpk_facebookInvite {
    [XXGPlayKitCore xxpk_facebookInvite];
}
#endif

@end
