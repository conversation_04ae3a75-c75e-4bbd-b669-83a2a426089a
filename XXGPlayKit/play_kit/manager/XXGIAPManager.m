//
//  XXGIAPManager.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/18.
//

#import "XXGIAPManager.h"
#import "NSObject+XXGModel.h"
#import "NSString+XXGString.h"
#import "XXGAlertView.h"
#import "XXGBoxManager.h"
#import "XXGIAPHelp.h"
#import "XXGLoadingView.h"
#import "XXGNetworkList.h"
#import "XXGPlayKitConfig.h"
#import "XXGPlayKitCore+Canal.h"
#import "XXGPlayKitCore.h"
#import "XXGProductBody.h"
#import "XXGSelectProduct.h"
#import "XXGSelectProductItem.h"
#import "XXGUIkitProtocol.h"
#import "XXGValidateReceiptBody.h"
#import "ZBObjectiveCBeaver.h"

#define weakify(obj) __weak typeof(obj) weak##obj = obj;
#define strongify(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGIAPManager () <XXGIAPPayDelegate, XXGUIkitDelegate>

@end

@implementation XXGIAPManager

+ (instancetype)shared
{
    static id shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{ shared = [[super allocWithZone:NULL] init]; });
    return shared;
}

+ (void)xxpk_iapRepair
{
    [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_repair_start];
    NSArray *transactions = [SKPaymentQueue defaultQueue].transactions;
    if (transactions.count > 0)
    {
        for (int i = 0; i < transactions.count; i++)
        {
            SKPaymentTransaction *transaction = transactions[i];
            [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
        }
    }
    [[XXGIAPHelpManager sharedManager] cleanAllModels];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(),
                   ^{ [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_repair_complete]; });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(),
                   ^{ [XXGLoadingView hideLoadingFromWindow]; });
}

- (void)xxpk_registerP
{
    [XXGIAPHelpManager sharedManager].delegate = self;
    [[XXGIAPHelpManager sharedManager] registerPay];
}

- (void)xxpk_createOrder:(XXGProductBody *)item xxpk_isCoinOrder:(BOOL)isCoin
{
    if (item.xxpk_amount.xxpk_isEmpty || item.xxpk_cpOrderId.xxpk_isEmpty || item.xxpk_productCode.xxpk_isEmpty ||
        item.xxpk_productName.xxpk_isEmpty || item.xxpk_serverId.xxpk_isEmpty)
    {
        [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_params_error];
        return;
    }

    self.xxpk_isCoinOrder = isCoin;
    weakify(self);
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkCreateOrder:isCoin params:[item xxpk_modelToDict]
        success:^(NSDictionary *_Nonnull responseObject) {
            XXGSelectProduct *xxpk_product = [XXGSelectProduct
                xxpk_modelWithDict:responseObject[__data_core.xxpk_order]];

            weakself.xxpk_item = item;
            weakself.xxpk_item.xxpk_orderId = xxpk_product.xxpk_orderId;
            weakself.xxpk_item.xxpk_currency = xxpk_product.xxpk_currency;

            if (xxpk_product.xxpk_pay_method.count == 0)
            {
                [weakself.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_not_config];
                return;
            }

            // 如果只有一种支付方式，通常不需要展示选择界面，除非这一种是海外支付并且note字段不为空
            if (xxpk_product.xxpk_pay_method.count == 1)
            {
                XXGSelectProductItem *firstPayMethod = xxpk_product.xxpk_pay_method[0];

                // 判断是否需要显示选择界面
                BOOL hasNote = firstPayMethod.xxpk_note && !firstPayMethod.xxpk_note.xxpk_isEmpty;
                BOOL hasDiscountText = xxpk_product.xxpk_discount_text && !xxpk_product.xxpk_discount_text.xxpk_isEmpty;
                BOOL shouldShowSelectView = (hasNote || hasDiscountText) && xxpk_product.xxpk_display_view;

                if (shouldShowSelectView)
                {
                    [[XXGPlayKitCore shared] xxpk_showUIofSelectPayMethod:xxpk_product xxpk_delegate:self];
                }
                else
                {
                    [weakself _xxpk_selectPBeforeExtraWithSPItme:firstPayMethod xxpk_productCode:item.xxpk_productCode
                                                    xxpk_orderId:self.xxpk_item.xxpk_orderId];
                }
            }
            else
            {
                [[XXGPlayKitCore shared] xxpk_showUIofSelectPayMethod:xxpk_product xxpk_delegate:self];
            }
        } failure:^(NSError *_Nonnull error) {
            NSString *errorDescrip = [NSString
                stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
            [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
        }];
}

- (void)_xxpk_selectPBeforeExtraWithSPItme:(XXGSelectProductItem *)item
                          xxpk_productCode:(NSString *)xxpk_productCode
                              xxpk_orderId:(NSString *)xxpk_orderId
{
#ifdef XXGPLAYKITCN_TARGET
    // 国内优惠券展示
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkOrderExtra:xxpk_orderId pmethod:item.xxpk_type
        success:^(NSDictionary *_Nonnull responseObject) {
            if (responseObject[__data_core.xxpk_core_mt_popup])
            {
                NSMutableDictionary *mutDic = [responseObject[__data_core.xxpk_core_mt_popup] mutableCopy];
                mutDic[__data_core.xxpk_p_block_orderExtra] = ^(BOOL iscontinue) {
                    if (iscontinue)
                    {
                        [self _xxpk_selectPWithSPItme:item xxpk_productCode:xxpk_productCode xxpk_orderId:xxpk_orderId];
                    }
                    else
                    {
                        [self.xxpk_delegate xxpk_iapManagerCancel:self];
                    }
                };
                [XXGPlayKitCore.shared xxpk_showUIofPopup:mutDic];
            }
            else
            {
                [self _xxpk_selectPWithSPItme:item xxpk_productCode:xxpk_productCode xxpk_orderId:xxpk_orderId];
            }
        } failure:^(NSError *_Nonnull error) {
            [self _xxpk_selectPWithSPItme:item xxpk_productCode:xxpk_productCode xxpk_orderId:xxpk_orderId];
        }];
#endif
#ifdef XXGPLAYKITOS_TARGET
    [self _xxpk_selectPWithSPItme:item xxpk_productCode:xxpk_productCode xxpk_orderId:xxpk_orderId];
#endif
}

/// 选择支付完成去支付
- (void)_xxpk_selectPWithSPItme:(XXGSelectProductItem *)item
               xxpk_productCode:(NSString *)xxpk_productCode
                   xxpk_orderId:(NSString *)xxpk_orderId
{
    // 渠道
    if ([[XXGPlayKitCore shared] _xxpk_canal_selectPWithSPItme:item pitem:self.xxpk_item])
    {
        return;
    }

    // iap
    if ([item.xxpk_type containsString:__data_core.xxpk_iap])
    {
        [[XXGIAPHelpManager sharedManager] buyProductWithUserID:[XXGBoxManager xxpk_comeinedBox].xxpk_boxId
                                              productIdentifier:xxpk_productCode
                                                   xxpk_orderId:xxpk_orderId];
        return;
    }

    // h5
    if ([item.xxpk_type containsString:__data_core.xxpk_h5])
    {
        [self.xxpk_delegate xxpk_IAPManagerOpenOfOrderUrl:item.xxpk_order_url];
        [self __showCheckH5OrderAlertWith:xxpk_orderId];
        return;
    }

    [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_notype];
}

- (void)__showCheckH5OrderAlertWith:(NSString *)xxpk_orderId
{
    [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:__string_core.xxpk_p_pornop
                             buttonTitles:@[ __string_core.xxpk_p_cancel, __string_core.xxpk_p_sustip ]
                               completion:^(NSInteger buttonIndex) {
                                   if (buttonIndex == 0)
                                   {
                                       [self.xxpk_delegate xxpk_iapManagerCancel:self];
                                   }
                                   else
                                   {
                                       [self __checkH5Order:xxpk_orderId];
                                   }
                               }];
}

- (void)__checkH5Order:(NSString *)xxpk_orderId
{
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkCheckOrderWithIsCoin:self.xxpk_isCoinOrder
        xxpk_orderId:xxpk_orderId success:^(NSDictionary *_Nonnull responseObject) {
            NSInteger status = [responseObject[__data_core.xxpk_order][__data_core.xxpk_p_status] integerValue];
            if (status == 1)
            {
                [self.xxpk_delegate xxpk_iapManager:self paySuccessWithItem:self.xxpk_item];
            }
            else
            {
                [self.xxpk_delegate xxpk_iapManagerCancel:self];
            }
        } failure:^(NSError *_Nonnull error) {
            NSString *errorDescrip = [NSString
                stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
            [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
        }
        retryCount:10
        currentAttempt:0];
}

// iap 单订验证
- (void)__checkIAPOrder:(XXGValidateReceiptBody *)model resultAction:(VerifyRsultBlock)resultAction
{
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkCheckOrderWithIsCoin:self.xxpk_isCoinOrder
        xxpk_orderId:model.xxpk_order_id success:^(NSDictionary *_Nonnull responseObject) {
            NSInteger status = [responseObject[__data_core.xxpk_order][__data_core.xxpk_p_status] integerValue];
            if (status == -1)
            {
                resultAction(XXGIAPVerifyInvalid);
                [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_error];
            }
            else if (status == 1)
            {
                resultAction(XXGIAPVerifyValid);
                [self.xxpk_delegate xxpk_iapManager:self paySuccessWithItem:self.xxpk_item];
            }
            else
            {
                [self __checkIAPOrder:model resultAction:resultAction];
            }
        } failure:^(NSError *_Nonnull error) {
            if (error.code == __data_core.xxpk_net_code_error)
            {
                resultAction(XXGIAPVerifyInvalid);
                NSString *errorDescrip = [NSString
                    stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
                [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
            }
            else
            {
                [self __checkIAPOrder:model resultAction:resultAction];
            }
        }
        retryCount:10
        currentAttempt:0];
}

- (void)__uploadReceiptWithModel:(XXGValidateReceiptBody *)model resultAction:(VerifyRsultBlock)resultAction
{
    [self __uploadReceiptWithModel:model resultAction:resultAction retryCount:3 currentAttempt:0];
}

- (void)__uploadReceiptWithModel:(XXGValidateReceiptBody *)model
                    resultAction:(VerifyRsultBlock)resultAction
                      retryCount:(NSInteger)retryCount
                  currentAttempt:(NSInteger)currentAttempt
{
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish)
    {
        return;
    }

    // 数据校验：确保必要字段不为空
    if (!model.xxpk_order_id || model.xxpk_order_id.xxpk_isEmpty) {
        ZBLogInfo(@"[IAP] 验证失败：订单ID为空，无法进行验证");
        if (resultAction) {
            resultAction(XXGIAPVerifyInvalid);
        }
        return;
    }

    if (!model.xxpk_receipt_data || model.xxpk_receipt_data.xxpk_isEmpty) {
        ZBLogInfo(@"[IAP] 验证失败：收据数据为空，无法进行验证");
        if (resultAction) {
            resultAction(XXGIAPVerifyInvalid);
        }
        return;
    }

    ZBLogInfo(@"[IAP] 开始验证收据，订单ID：%@，重试次数：%ld/%ld", model.xxpk_order_id, (long)currentAttempt + 1, (long)retryCount + 1);

    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkValidateReceipt:[model xxpk_modelToDict] success:^(NSDictionary *_Nonnull responseObject) {
        ZBLogInfo(@"[IAP] 收据验证成功，订单ID：%@", model.xxpk_order_id);
        [self __checkIAPOrder:model resultAction:resultAction];
    }failure:^(NSError *_Nonnull error) {
        ZBLogInfo(@"[IAP] 收据验证失败，订单ID：%@，错误：%@", model.xxpk_order_id, error.localizedDescription);

        // 检查是否还有重试机会
        if (currentAttempt < retryCount) {
            ZBLogInfo(@"[IAP] 准备重试验证，订单ID：%@，当前重试：%ld/%ld", model.xxpk_order_id, (long)currentAttempt + 1, (long)retryCount);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self __uploadReceiptWithModel:model resultAction:resultAction retryCount:retryCount currentAttempt:currentAttempt + 1];
            });
        } else {
            ZBLogInfo(@"[IAP] 验证重试次数已用完，订单ID：%@，最终失败", model.xxpk_order_id);
            if (resultAction) {
                resultAction(XXGIAPVerifyInvalid);
            }
        }
    }];
}

// MARK: - SelectPayMethod UI Delegate
// 点击支付
- (void)xxpk_selectPayMethodPayButtonDidClickOfProductItem:(XXGSelectProductItem *)productItem
{
    [self _xxpk_selectPBeforeExtraWithSPItme:productItem xxpk_productCode:self.xxpk_item.xxpk_productCode
                                xxpk_orderId:self.xxpk_item.xxpk_orderId];
}

// 关闭
- (void)xxpk_selectPayMethodCloseButtonDidClick
{
    [self.xxpk_delegate xxpk_iapManagerCancel:self];
}

// MARK: - XXGIAPPayDelegate
- (void)verifyWithModel:(XXGIAPTransactionModel *)model resultAction:(VerifyRsultBlock)resultAction
{
    ZBLogInfo(@"[IAP] 开始构建验证数据，交易ID：%@", model.xxpk_transactionIdentifier);

    XXGValidateReceiptBody *body = [[XXGValidateReceiptBody alloc] init];

    // 优先使用 _xxpk_item 中的订单ID，确保数据一致性
    NSString *orderId = nil;
    if (_xxpk_item && _xxpk_item.xxpk_orderId && !_xxpk_item.xxpk_orderId.xxpk_isEmpty) {
        orderId = _xxpk_item.xxpk_orderId;
        ZBLogInfo(@"[IAP] 使用现有订单ID：%@", orderId);
    } else if (model.xxpk_seriverOrder && !model.xxpk_seriverOrder.xxpk_isEmpty) {
        orderId = model.xxpk_seriverOrder;
        ZBLogInfo(@"[IAP] 使用交易模型订单ID：%@", orderId);
    } else {
        ZBLogInfo(@"[IAP] 警告：无法获取有效的订单ID，交易ID：%@", model.xxpk_transactionIdentifier);
    }

    body.xxpk_order_id = orderId;
    body.xxpk_receipt_data = model.xxpk_appStoreReceipt;
    body.xxpk_product_code = model.xxpk_productIdentifier;
    body.xxpk_transaction_id = model.xxpk_transactionIdentifier;
    body.xxpk_price = model.xxpk_priceString;
    body.xxpk_currency = model.xxpk_codeString;

    // 确保 _xxpk_item 存在且数据完整
    if (!_xxpk_item)
    {
        _xxpk_item = [XXGProductBody new];
        _xxpk_item.xxpk_productCode = model.xxpk_productIdentifier;
        _xxpk_item.xxpk_orderId = orderId;
        _xxpk_item.xxpk_amount = model.xxpk_priceString;
        ZBLogInfo(@"[IAP] 创建新的商品信息，订单ID：%@", orderId);
    } else {
        // 更新现有数据，确保一致性
        if (!_xxpk_item.xxpk_orderId || _xxpk_item.xxpk_orderId.xxpk_isEmpty) {
            _xxpk_item.xxpk_orderId = orderId;
        }
        ZBLogInfo(@"[IAP] 更新现有商品信息，订单ID：%@", _xxpk_item.xxpk_orderId);
    }
    _xxpk_item.xxpk_currency = model.xxpk_codeString;

    [self __uploadReceiptWithModel:body resultAction:resultAction];
}

- (void)onIAPPayFailue:(XXGIAPTransactionModel *)model withError:(NSError *)error
{
    if (model.xxpk_transactionStatus == TransactionStatusAppleCancel)
    {
        [self.xxpk_delegate xxpk_iapManagerCancel:self];
    }
    else
    {
        NSString *errorDescrip = [NSString
            stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
    }
    if (error.code == XXGIAPErrorCodeHasUnfinishedTransaction)
    {
        [[XXGIAPHelpManager sharedManager] checkUnfinishTransaction];
    }
}

- (void)onLaunProductListFinish:(SKProduct *)products withError:(NSError *)error
{
    NSString *errorDescrip = [NSString
        stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
    [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
}

- (void)currentStatus:(XXGIAPLoadingStatus)status
{
    switch (status)
    {
    case XXGIAPLoadingStatus_CheckingProduct:
        [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_checkingproduct];
        break;
    case XXGIAPLoadingStatus_Paying:
        [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_paying];
        break;
    case XXGIAPLoadingStatus_Restoring:
        [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_restoring];
        break;
    case XXGIAPLoadingStatus_Verifying:
        [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_verifying];
        break;
    default:
        break;
    }
}
@end
