//
//  XXGVKManager.h
//  XXGOSPlayKit
//
//  Created by apple on 2025/3/26.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGVKManager : NSObject

+ (void)xxpk_oauthOnViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler;

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)xxpk_startVKWithClientID:(NSString *)clientId clientSecret:(NSString *)clientSecret;

+ (void)xxpk_jumpToVKAndFollw:(NSString *)vkhome;
@end

NS_ASSUME_NONNULL_END
