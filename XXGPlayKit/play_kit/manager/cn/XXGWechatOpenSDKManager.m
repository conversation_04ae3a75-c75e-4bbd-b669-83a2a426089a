//
//  XXGWechatOpenSDKManager.m
//  XXGPlayKitCN
//
//  Created by apple on 2025/6/20.
//

#import "XXGWechatOpenSDKManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGWechatOpenSDKManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(__data_core.xxpk_middleware_wechatopensdk);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ZBLogInfo(__data_core.xxpk_log_manager_weichatopensdk,class?__data_core.xxpk_manager_status_exist:__data_core.xxpk_manager_status_not_exist);
    });
    return class;
}

+ (void)xxpk_sendReqWithQuery:(NSString *)query {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_sendReqWithQuery:) withObject:query];
    }
}

+ (void)xxpk_registerApp:(NSString *)wxAppId universalLink:(NSString *)universalLink {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_registerApp:universalLink:) withObject:wxAppId withObject:universalLink];
    }
}

+ (void)xxpk_handleOpenUniversalLink:(NSUserActivity *)userActivity {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_handleOpenUniversalLink:) withObject:userActivity];
    }
}
@end
