//
//  XXGSigmobManager.h
//  XXGPlayKitCN
//
//  Created by apple on 2025/7/1.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGSigmobManager : NSObject

+ (void)xxpk_sigmobInitWithAppId:(NSString *)appId appKey:(NSString *)appKey;

+ (void)xxpk_sigmoLoadAdWithPlacementId:(NSString *)placementId uid:(NSString *)uid options:(NSDictionary *)options;

+ (void)xxpk_sigmobShowAdFromRootViewController:(UIViewController *)rootViewController complate:(void(^)(BOOL result))complate;
@end

NS_ASSUME_NONNULL_END
