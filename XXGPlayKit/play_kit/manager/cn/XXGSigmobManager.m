//
//  XXGSigmobManager.m
//  XXGPlayKitCN
//
//  Created by apple on 2025/7/1.
//

#import "XXGSigmobManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGSigmobManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(__data_core.xxpk_middleware_sigmob);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ZBLogInfo(__data_core.xxpk_log_manager_sigmob,class?__data_core.xxpk_manager_status_exist:__data_core.xxpk_manager_status_not_exist);
    });
    if (class) {
        return [class xxpk_performSelector:@selector(shared)];
    }
    return nil;
}

+ (void)xxpk_sigmobInitWithAppId:(NSString *)appId appKey:(NSString *)appKey {  // SDK初始化接口
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_sigmobInitWithAppId:appKey:) withObject:appId withObject:appKey];
    }
}

+ (void)xxpk_sigmoLoadAdWithPlacementId:(NSString *)placementId uid:(NSString *)uid options:(NSDictionary *)options {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_sigmoLoadAdWithPlacementId:uid:options:) withObject:placementId withObject:uid withObject:options];
    }
}

+ (void)xxpk_sigmobShowAdFromRootViewController:(UIViewController *)rootViewController complate:(void(^)(BOOL result))complate {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_sigmobShowAdFromRootViewController:complate:) withObject:rootViewController withObject:complate];
    }
}
@end
