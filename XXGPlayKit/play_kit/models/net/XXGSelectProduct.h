//
//  XXGSelectProduct.h
//  XXGPlayKit
//
//  Created by apple on 2025/5/13.
//

#import <Foundation/Foundation.h>
#import "XXGSelectProductItem.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGSelectProduct : NSObject

@property (nonatomic,copy) NSString * xxpk_currency;
@property (nonatomic,copy) NSString * xxpk_orderId;
@property (nonatomic, strong) NSArray <XXGSelectProductItem *> *xxpk_pay_method;

// 海外字段
@property (nonatomic,copy) NSString * xxpk_price;
// 国内字段
@property (nonatomic,copy) NSString * xxpk_amount;

// 折扣
@property (nonatomic,copy) NSString * xxpk_amount_text;
@property (nonatomic,copy) NSString * xxpk_discount_text;

// 是否显示
@property (nonatomic,assign) BOOL xxpk_display_view;

@end

NS_ASSUME_NONNULL_END
