//
//  ZBLog.h
//  Pods-ZBLog_Example
//
//  Created by <PERSON><PERSON> on 2021/3/10.
//

#import <Foundation/Foundation.h>

@class ZBBaseDestination;;

NS_ASSUME_NONNULL_BEGIN

/**
 *  Flags accompany each log. They are used together with levels to filter out logs.
 */
typedef NS_OPTIONS(NSUInteger, ZBLogFlag){
    /**
     *  0...00001 ZBLogFlagError
     */
    ZBLogFlagError      = (1 << 0),

    /**
     *  0...00010 ZBLogFlagWarning
     */
    ZBLogFlagWarning    = (1 << 1),

    /**
     *  0...00100 ZBLogFlagInfo
     */
    ZBLogFlagInfo       = (1 << 2),

    /**
     *  0...01000 ZBLogFlagDebug
     */
    ZBLogFlagDebug      = (1 << 3),

    /**
     *  0...10000 ZBLogFlagVerbose
     */
    ZBLogFlagVerbose    = (1 << 4)
};

/**
 *  Log levels are used to filter out logs. Used together with flags.
 */
typedef NS_ENUM(NSUInteger, ZBLogLevel){
    /**
     *  No logs
     */
    ZBLogLevelOff       = 0,

    /**
     *  <PERSON><PERSON><PERSON> logs only
     */
    ZBLogLevelError     = (ZBLogFlagError),

    /**
     *  Error and warning logs
     */
    ZBLogLevelWarning   = (ZBLogLevelError   | ZBLogFlagWarning),

    /**
     *  Error, warning and info logs
     */
    ZBLogLevelInfo      = (ZBLogLevelWarning | ZBLogFlagInfo),

    /**
     *  Error, warning, info and debug logs
     */
    ZBLogLevelDebug     = (ZBLogLevelInfo    | ZBLogFlagDebug),

    /**
     *  Error, warning, info, debug and verbose logs
     */
    ZBLogLevelVerbose   = (ZBLogLevelDebug   | ZBLogFlagVerbose),

    /**
     *  All logs (1...11111)
     */
    ZBLogLevelAll       = NSUIntegerMax
};

@interface ZBLog : NSObject

/**
 *  Returns the singleton `ZBLog`.
 *  The instance is used by `ZBLog` class methods.
 */
@property (class, nonatomic, strong, readonly) ZBLog *zb_sharedInstance;

/// a set of active destinations
@property (nonatomic, strong, readonly) NSMutableSet *zb_destinations;

/// returns boolean about success
+ (BOOL)zb_addDestination:(ZBBaseDestination *)zb_destination;

/// returns boolean about success
+ (BOOL)zb_removeDestination:(ZBBaseDestination *)zb_destination;

/// if you need to start fresh
+ (void)zb_removeAllDestinations;

/// returns the amount of destinations
+ (NSInteger)zb_countDestinations;

/// custom logging to manually adjust values, should just be used by other frameworks
+ (void)zb_custom:(ZBLogLevel)zb_level
          zb_file:(const char *)zb_file
      zb_function:(const char *)zb_function
          zb_line:(NSUInteger)zb_line
       zb_context:(nullable id)zb_context
        zb_format:(NSString *)zb_format, ... ;

@end

NS_ASSUME_NONNULL_END
