//
//  ZBLogFormatter.h
//  XXGPlayKit
//
//  Created by AI Assistant on 2025/1/20.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 日志格式化工具
 */
@interface ZBLogFormatter : NSObject

/**
 * 格式化字典/数组为易读的字符串
 * @param obj 要格式化的对象（字典、数组、或其他对象）
 * @return 格式化后的字符串
 */
+ (NSString *)formatObject:(nullable id)obj;

/**
 * 格式化字典为易读的字符串
 * @param dict 要格式化的字典
 * @return 格式化后的字符串
 */
+ (NSString *)formatDictionary:(nullable NSDictionary *)dict;

/**
 * 格式化字典为易读的字符串（带缩进和深度限制）
 * @param dict 要格式化的字典
 * @param indent 缩进级别
 * @param maxDepth 最大嵌套深度
 * @return 格式化后的字符串
 */
+ (NSString *)formatDictionary:(nullable NSDictionary *)dict withIndent:(NSInteger)indent maxDepth:(NSInteger)maxDepth;

/**
 * 格式化数组为易读的字符串
 * @param array 要格式化的数组
 * @return 格式化后的字符串
 */
+ (NSString *)formatArray:(nullable NSArray *)array;

/**
 * 格式化数组为易读的字符串（带缩进和深度限制）
 * @param array 要格式化的数组
 * @param indent 缩进级别
 * @param maxDepth 最大嵌套深度
 * @return 格式化后的字符串
 */
+ (NSString *)formatArray:(nullable NSArray *)array withIndent:(NSInteger)indent maxDepth:(NSInteger)maxDepth;

/**
 * 格式化网络请求参数
 * @param params 请求参数字典
 * @return 格式化后的字符串
 */
+ (NSString *)formatRequestParams:(nullable NSDictionary *)params;

/**
 * 格式化网络响应数据
 * @param response 响应数据
 * @return 格式化后的字符串
 */
+ (NSString *)formatResponse:(nullable id)response;

/**
 * 格式化错误信息
 * @param error 错误对象
 * @return 格式化后的字符串
 */
+ (NSString *)formatError:(nullable NSError *)error;

@end

/**
 * 全局格式化函数（供宏使用）
 */
NSString* ZBFormatDict(id _Nullable obj);

NS_ASSUME_NONNULL_END
