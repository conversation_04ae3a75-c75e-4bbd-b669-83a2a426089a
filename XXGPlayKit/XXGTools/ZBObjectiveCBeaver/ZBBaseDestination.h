//
//  ZBBaseDestination.h
//  Pods-ZBLog_Example
//
//  Created by <PERSON><PERSON> on 2021/3/10.
//

#import <Foundation/Foundation.h>

#import "ZBLog.h"


NS_ASSUME_NONNULL_BEGIN

@interface ZBLevelString : NSObject
@property (nonatomic, copy) NSString *zb_verbose;
@property (nonatomic, copy) NSString *zb_debug;
@property (nonatomic, copy) NSString *zb_info;
@property (nonatomic, copy) NSString *zb_warning;
@property (nonatomic, copy) NSString *zb_error;
@property (nonatomic, copy) NSString *zb_all;
@end

@interface ZBLevelColor : NSObject
@property (nonatomic, copy) NSString *zb_verbose;
@property (nonatomic, copy) NSString *zb_debug;
@property (nonatomic, copy) NSString *zb_info;
@property (nonatomic, copy) NSString *zb_warning;
@property (nonatomic, copy) NSString *zb_error;
@property (nonatomic, copy) NSString *zb_all;
@end

@interface ZBBaseDestination : NSObject

/**
 each destination instance must have an own serial queue to ensure serial output
 GCD gives it a prioritization between User Initiated and Utility
 */
@property (nonatomic, strong, readonly) dispatch_queue_t zb_queue;

/// do not log any message which has a lower level than this one
@property (nonatomic, assign) ZBLogLevel zb_minLevel;

/// runs in own serial background thread for better performance
@property (nonatomic, assign) BOOL zb_asynchronously;

/// set custom log level words for each level
@property (nonatomic, strong) ZBLevelString *zb_levelString;

// For a colored log level word in a logged line
// empty on default
@property (nonatomic, strong) ZBLevelColor *zb_levelColor;

/// returns a formatted date string
/// optionally in a given abbreviated timezone like "UTC"
- (NSString *)formatDate:(NSString *)dateFormat timeZone:(nullable NSString *)timeZone;

// returns color string for level
- (NSString *)colorForLevel:(ZBLogLevel)level;

/// send / store the formatted log message to the destination
/// returns the formatted log message for processing by inheriting method
/// and for unit tests (nil if error)
- (NSString *)zb_send:(ZBLogLevel)zb_level
               zb_msg:(NSString *)zb_msg
            zb_thread:(NSString *)zb_thread
              zb_file:(NSString *)zb_file
          zb_function:(NSString *)zb_function
              zb_line:(NSUInteger)zb_line
           zb_context:(id)zb_context;

/// checks if level is at least minLevel or if a minLevel filter for that path does exist
/// returns boolean and can be used to decide if a message should be logged or not
- (BOOL)zb_shouldLevelBeLogged:(ZBLogLevel)zb_level
                       zb_path:(NSString *)zb_path
                   zb_function:(NSString *)zb_function
                    zb_message:(NSString *)zb_message;
@end

NS_ASSUME_NONNULL_END
