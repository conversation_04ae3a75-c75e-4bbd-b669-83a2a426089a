//
//  ZBLogMacros.h
//  Pods
//
//  Created by <PERSON><PERSON> on 2021/3/13.
//

#ifndef Z<PERSON>ogMac<PERSON>_h
#define ZBLogMacros_h

#import "ZBLog.h"
#import "ZBLogFormatter.h"
#import "XXGPlayKitConfig.h"

/**
 * These are the two macros that all other macros below compile into.
 * These big multiline macros makes all the other macros easier to read.
 **/
#define LOG_MACRO(lvl, fnct, ctx, frmt, ...)   \
        [ZBLog zb_custom : lvl                    \
                 zb_file : __FILE__               \
             zb_function : fnct                   \
                 zb_line : __LINE__               \
              zb_context : ctx                    \
               zb_format : (frmt), ## __VA_ARGS__]

/**
 * Ready to use log macros with no context or tag.
 **/
#define LOG_MAYBE(lvl, fnct, ctx, frmt, ...) \
        do { if((lvl) != 0) LOG_MACRO(lvl, fnct, ctx, frmt, ##__VA_ARGS__); } while(0)

#define ZBLogError(frmt, ...)     LOG_MAYBE(ZBLogLevelError,   __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define ZBLogWarn(frmt, ...)      LOG_MAYBE(ZBLogLevelWarning, __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define ZBLogInfo(frmt, ...)      LOG_MAYBE(ZBLogLevelInfo,    __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define ZBLogDebug(frmt, ...)     LOG_MAYBE(ZBLogLevelDebug,   __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define ZBLogVerbose(frmt, ...)   LOG_MAYBE(ZBLogLevelVerbose, __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)

// 字典格式化日志宏
#define ZBLogErrorDict(msg, dict)     ZBLogError(@"%@\n%@", msg, ZBFormatDict(dict))
#define ZBLogWarnDict(msg, dict)      ZBLogWarn(@"%@\n%@", msg, ZBFormatDict(dict))
#define ZBLogInfoDict(msg, dict)      ZBLogInfo(@"%@\n%@", msg, ZBFormatDict(dict))
#define ZBLogDebugDict(msg, dict)     ZBLogDebug(@"%@\n%@", msg, ZBFormatDict(dict))
#define ZBLogVerboseDict(msg, dict)   ZBLogVerbose(@"%@\n%@", msg, ZBFormatDict(dict))

// 网络请求专用日志宏
#define ZBLogRequest(url, params)     ZBLogInfo(__data_core.xxpk_tools_logger_request_format, url, ZBFormatDict(params))
#define ZBLogResponse(url, response)  ZBLogInfo(__data_core.xxpk_tools_logger_response_format, url, ZBFormatDict(response))
#define ZBLogNetworkError(url, error) ZBLogError(__data_core.xxpk_tools_logger_network_error_format, url, ZBFormatDict(error))

// 字典格式化函数声明
NSString* ZBFormatDict(id obj);

#endif /* ZBLogMacros_h */
