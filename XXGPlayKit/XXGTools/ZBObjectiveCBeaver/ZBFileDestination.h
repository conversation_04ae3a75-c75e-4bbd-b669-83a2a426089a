//
//  ZBFileDestination.h
//  ZBObjectiveCBeaver
//
//  Created by Ju<PERSON> on 2021/3/16.
//

#import "ZBBaseDestination.h"

NS_ASSUME_NONNULL_BEGIN

@interface ZBFileDestination : ZBBaseDestination

/// 日志文件URL
@property (nonatomic, strong) NSURL *logFileURL;

/// 是否每次写入后同步
@property (nonatomic, assign) BOOL syncAfterEachWrite;

/// 最大保存天数（默认7天）
@property (nonatomic, assign) NSInteger maxDays;

/// 是否启用加密存储（默认NO）
@property (nonatomic, assign) BOOL encryptionEnabled;

/**
 * 获取所有日志文件
 */
- (NSArray<NSURL *> *)allLogFiles;

/**
 * 读取指定文件的日志内容
 */
- (NSString *)readLogFile:(NSURL *)fileURL;

/**
 * 读取所有日志内容
 */
- (NSString *)readAllLogs;

/**
 * 读取所有日志的原始内容（保持存储格式，不解密）
 */
- (NSString *)readAllLogsRaw;

/**
 * 读取指定日期的日志内容
 */
- (NSString *)readLogsForDate:(NSDate *)date;

/**
 * 获取所有有日志的日期
 */
- (NSArray<NSDate *> *)allLogDates;

/**
 * 清理过期日志文件
 */
- (void)cleanupOldLogs;

/**
 * 获取当前日志文件URL
 */
- (NSURL *)currentLogFileURL;

@end

NS_ASSUME_NONNULL_END
