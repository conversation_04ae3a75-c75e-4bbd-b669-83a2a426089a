//
//  XXGAppInfo.h
//  XXGPlayKit
//
//  Created by apple on 2025/2/25.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGAppInfo : NSObject

@property (class, nonatomic, readonly, strong) UIImage *xxpk_getAppIconImage;

@property (class, nonatomic, readonly, copy) NSString *xxpk_appBundleIdentifier;

@property (class, nonatomic, readonly, copy) NSString *xxpk_appVersion;

@property (class, nonatomic, readonly, copy) NSString *xxpk_appName;

@property (class, nonatomic, readonly, copy) NSString *xxpk_deviceName;

@property (class, nonatomic, readonly, copy) NSString *xxpk_deviceIdfa;

@property (class, nonatomic, readonly, copy) NSString *xxpk_deviceIdfv;

@property (class, nonatomic, readonly, copy) NSString *xxpk_deviceModel;

@property (class, nonatomic, readonly, copy) NSString *xxpk_systemVersion;

@property (class, nonatomic, readonly, copy) NSString *xxpk_applicationPath;

+ (void)xxpk_requestIDFAIfNeeded:(void (^)(void))complate;

@end

NS_ASSUME_NONNULL_END
