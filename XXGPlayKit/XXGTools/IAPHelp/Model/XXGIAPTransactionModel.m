//
//  XXGIAPTransactionModel.m
//  XXGIAPHelp
//
//  Created by kane on 2018/8/8.
//  Copyright © 2018年 kane. All rights reserved.
//

#import "XXGIAPTransactionModel.h"
#import "XXGIAPConfig.h"
#import "XXGPlayKitConfig.h"

@interface XXGIAPTransactionModel ()
@end

@implementation XXGIAPTransactionModel

+ (instancetype)xxpk_modelWithProductIdentifier:(NSString *)productIdentifier applicationUsername:(NSString *)applicationUsername {
    NSParameterAssert(productIdentifier);
    XXGIAPTransactionModel *model = [XXGIAPTransactionModel new];
    model.xxpk_productIdentifier = productIdentifier;
    model.xxpk_applicationUsername = applicationUsername;
    model.xxpk_transactionStatus = 0;
    model.xxpk_transactionDate = [NSDate date];

    if (applicationUsername) {
        NSError *error = nil;
        NSData *data = [applicationUsername dataUsingEncoding:NSUTF8StringEncoding];
        if (data) {
            NSDictionary *XXGIAPInfo = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableLeaves error:&error];
            if (!error && [XXGIAPInfo isKindOfClass:[NSDictionary class]]) {
                model.xxpk_priceString = [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_priceString];
                model.xxpk_seriverOrder =  [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_seriverOrder];
                model.xxpk_userId =  [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_userId];
                model.xxpk_codeString = [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_codeString];
            }
        }
    }
    return model;
}

+ (XXGIAPTransactionModel *)xxpk_modelWithDic:(NSDictionary *)dic {
    XXGIAPTransactionModel *model = [[XXGIAPTransactionModel alloc] init];
    model.xxpk_productIdentifier = dic[__data_core.xxpk_tools_iap_productIdentifier];
    model.xxpk_applicationUsername = dic[__data_core.xxpk_tools_iap_applicationUsername];
    model.xxpk_transactionStatus = [dic[__data_core.xxpk_tools_iap_transactionStatus] integerValue];
    model.xxpk_transactionDate =  [NSDate dateWithTimeIntervalSince1970:[dic[__data_core.xxpk_tools_iap_transactionDate] doubleValue]];
    return model;
}

- (NSMutableDictionary *)xxpk_toDic {
    NSMutableDictionary *mutdic = [[NSMutableDictionary alloc] init];
    mutdic[__data_core.xxpk_tools_iap_productIdentifier] = self.xxpk_productIdentifier;
    mutdic[__data_core.xxpk_tools_iap_applicationUsername] = self.xxpk_applicationUsername;
    mutdic[__data_core.xxpk_tools_iap_transactionStatus] = @(self.xxpk_transactionStatus);
    mutdic[__data_core.xxpk_tools_iap_transactionDate] = @([self.xxpk_transactionDate timeIntervalSince1970]);
    return mutdic;
}

#pragma mark - Private

- (BOOL)isEqual:(id)object {
    if (!object) {
        return NO;
    }

    if (self == object) {
        return YES;
    }

    if (![object isKindOfClass:[XXGIAPTransactionModel class]]) {
        return NO;
    }

    return [self isEqualToModel:((XXGIAPTransactionModel *)object)];
}

- (BOOL)isEqualToModel:(XXGIAPTransactionModel *)object {

    BOOL isProductIdentifierMatch = [self.xxpk_productIdentifier isEqualToString:object.xxpk_productIdentifier];

    // 优先使用 transactionIdentifier 进行比较（最可靠的唯一标识）
    if (self.xxpk_transactionIdentifier && object.xxpk_transactionIdentifier) {
        return isProductIdentifierMatch && [self.xxpk_transactionIdentifier isEqualToString:object.xxpk_transactionIdentifier];
    }

    // 如果 transactionIdentifier 不可用，使用 applicationUsername 比较
    if (self.xxpk_applicationUsername && object.xxpk_applicationUsername) {
        return isProductIdentifierMatch && [self.xxpk_applicationUsername isEqualToString:object.xxpk_applicationUsername];
    }

    // 如果都不可用，使用 seriverOrder 比较
    if (self.xxpk_seriverOrder && object.xxpk_seriverOrder) {
        return isProductIdentifierMatch && [self.xxpk_seriverOrder isEqualToString:object.xxpk_seriverOrder];
    }

    // 最后使用产品ID和时间戳比较（不够可靠，但总比没有好）
    if (self.xxpk_transactionDate && object.xxpk_transactionDate) {
        NSTimeInterval timeDiff = fabs([self.xxpk_transactionDate timeIntervalSinceDate:object.xxpk_transactionDate]);
        return isProductIdentifierMatch && (timeDiff < 60.0); // 1分钟内的相同产品认为是同一交易
    }

    // 如果时间戳都不可用，只能认为不相等（保守策略）
    return NO;
}


#pragma mark -  Setter
- (void)setXxpk_userId:(NSString *)xxpk_userId {
    if (xxpk_userId) {
        _xxpk_userId = xxpk_userId;
    }
}
- (void)setXxpk_productIdentifier:(NSString *)xxpk_productIdentifier {
    if (xxpk_productIdentifier) {
        _xxpk_productIdentifier = xxpk_productIdentifier;
    }
}

-(void)setXxpk_transactionDate:(NSDate *)xxpk_transactionDate {
    if (xxpk_transactionDate) {
        _xxpk_transactionDate = xxpk_transactionDate;
    }
}

-(void)setXxpk_seriverOrder:(NSString *)xxpk_seriverOrder {
    if (xxpk_seriverOrder) {
        _xxpk_seriverOrder = xxpk_seriverOrder;
    }
}

-(void)setXxpk_applicationUsername:(NSString *)xxpk_applicationUsername {
    _xxpk_applicationUsername = xxpk_applicationUsername;
    if (xxpk_applicationUsername != nil) {
        NSError *error = nil;
        NSData *data = [xxpk_applicationUsername dataUsingEncoding:NSUTF8StringEncoding];
        if (data) {
            NSDictionary *XXGIAPInfo = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableLeaves error:&error];
            if (!error && [XXGIAPInfo isKindOfClass:[NSDictionary class]]) {
                _xxpk_priceString = [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_priceString];
                _xxpk_seriverOrder =  [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_seriverOrder];
                _xxpk_userId =  [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_userId];
                _xxpk_codeString = [XXGIAPInfo objectForKey:__data_core.xxpk_tools_iap_codeString];
            }
        }
    }
}

@end
