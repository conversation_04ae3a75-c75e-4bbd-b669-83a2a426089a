//
//  XXGIAPHelpManager.h
//  XXGIAPHelp
//
//  Created by kane on 2018/8/7.
//  Copyright © 2018年 kane. All rights reserved.
//

#import <UIKit/UiKit.h>
#import "XXGIAPPayProtocol.h"
#import "XXGIAPTransactionModel.h"
#import <StoreKit/StoreKit.h>
#import "XXGIAPVerifyManager.h"

NS_ASSUME_NONNULL_BEGIN

@class SKProduct;
@interface XXGIAPHelpManager : NSObject

/* 验证管理 */
@property (nonatomic,strong)XXGIAPVerifyManager *verifyManager;

/* 购买代理 */
@property (nonatomic,weak)id<XXGIAPPayDelegate> delegate;

/**
 * 单例方法.
 */
+ (instancetype)sharedManager;

/**
 注册支付事务监听, 并且开始支付凭证验证队列.
@warning ⚠️ 请在用户登录时和用户重新启动 APP 时调用.
 */
- (void)registerPay;

/**
 注册支付事务监听, 并且开始支付凭证验证队列.(指定钥匙串账号和服务区)

 @param keychainService keychainService
 @param keychainAccount keychainAccount
 */
- (void)registerPayWithKeychainService:(NSString *)keychainService
              keychainAccount:(NSString *)keychainAccount;

/**
 购买物品
@param userid 用户ID
 @param productIdentifier 物品id
 @param orderId 订单号
 */
- (void)buyProductWithUserID:(NSString *)userid
           productIdentifier:(NSString *)productIdentifier
                xxpk_orderId:(NSString *)xxpk_orderId;

/**
 购买物品
@param payment SKPayment
 */
- (void)buyProductWithSKPayment:(SKPayment  *)payment;
/**
 * 获取产品信息.
 *
 * @param productIdentifier 产品标识.
 */
- (void)fetchProductInfoWithProductIdentifier:(NSString *)productIdentifier;

/**
 恢复购买
 */
- (void)restoreProducts;

/// 检测未完成订单
-(void)checkUnfinishTransaction;

- (NSArray *)getUnfinishTransactions;

/// 重写本地票据到XXGIAP
/// @param keychainService 钥匙串
/// @param keychainAccount 钥匙串
/// @param models 钥匙串
- (void)resetKeychainService:( NSString *_Nullable)keychainService
             keychainAccount:( NSString *_Nullable)keychainAccount XXGIAPTransactionModels:(NSArray<XXGIAPTransactionModel *>*)models;


/**
⚠️ 删除所有已储存订单
 */
- (void)cleanAllModels;
@end

NS_ASSUME_NONNULL_END
