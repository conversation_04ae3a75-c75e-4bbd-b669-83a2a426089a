//
//  XXGWKScriptMessageHandler.h
//  XXGPlayKit
//
//  Created by apple on 2025/7/10.
//

#import <Foundation/Foundation.h>

#import <WebKit/WebKit.h>
#import <WebKit/WKFoundation.h>
NS_ASSUME_NONNULL_BEGIN

@interface XXGWKScriptMessageHandler : NSObject <WKScriptMessageHandler>
@property (nonatomic, weak) id<WKScriptMessageHandler> delegate;

- (instancetype)initWithDelegate:(id<WKScriptMessageHandler>)delegate;

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message;

@end

NS_ASSUME_NONNULL_END
