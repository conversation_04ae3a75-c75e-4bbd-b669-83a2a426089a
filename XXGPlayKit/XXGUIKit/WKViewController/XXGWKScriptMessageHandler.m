//
//  XXGWKScriptMessageHandler.m
//  XXGPlayKit
//
//  Created by apple on 2025/7/10.
//

#import "XXGWKScriptMessageHandler.h"

@implementation XXGWKScriptMessageHandler
- (instancetype)initWithDelegate:(id<WKScriptMessageHandler>)delegate {
    if (self = [super init]) {
        _delegate = delegate;
    }
    return self;
}

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    [self.delegate userContentController:userContentController didReceiveScriptMessage:message];
}
@end
