//
//  XXGSelectPayViewController.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/10.
//

#import "XXGSelectPayViewController.h"
#import "XXGSelectPayCell.h"
#import "NSString+XXGString.h"
#import "XXGSelectProduct.h"

@interface XXGSelectPayViewController ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) XXGSelectProduct *xxpk_product;

@property (nonatomic, strong) UITableView *xxpk_tableView;

@property (nonatomic, assign) NSInteger xxpk_selectIdx;

@property (nonatomic, strong) UIButton *xxpk_pButton;

@end

@implementation XXGSelectPayViewController

- (XXGSelectProduct *)xxpk_product {
    return self.xxpk_object;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.xxpk_closeButton.hidden = NO;
    
    UILabel *label = [UILabel new];
    label.text = XXGUIDriver.xxpk_string_ui.xxpk_p_pm;
    label.textColor = [XXGUIDriver xxpk_mainColor];
    label.font = [UIFont systemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float17];
    [self.view addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.xxpk_closeButton);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float35);
    }];
    
    self.view.clipsToBounds = YES;
    self.view.layer.cornerRadius = XXGUIDriver.xxpk_data_ui.xxpk_float4;
    
    NSString *xxpk_amout = self.xxpk_product.xxpk_amount?:self.xxpk_product.xxpk_price;
    
    _xxpk_pButton = [XXGUIDriver xxpk_buttonMainColor: [XXGUIDriver.xxpk_string_ui.xxpk_p_sup stringByAppendingFormat:@" %@",xxpk_amout]];

    [_xxpk_pButton addTarget:self action:@selector(xxpk_payButtonDidClicked:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:_xxpk_pButton];
    [_xxpk_pButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float48);
    }];

    // tableview
    _xxpk_tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
    _xxpk_tableView.backgroundColor = UIColor.systemGray6Color;
    _xxpk_tableView.contentInset = UIEdgeInsetsMake(0, 0, 10, 0);
    _xxpk_tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _xxpk_tableView.rowHeight = XXGUIDriver.xxpk_data_ui.xxpk_float70;
    _xxpk_tableView.delegate = self;
    _xxpk_tableView.dataSource = self;
    [_xxpk_tableView registerClass:[XXGSelectPayCell class] forCellReuseIdentifier:NSStringFromClass(XXGSelectPayCell.class)];

    _xxpk_tableView.tableHeaderView = [self xxpk_headerView];
    
    [self.view addSubview:_xxpk_tableView];
    [_xxpk_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(label.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float5);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(_xxpk_pButton.mas_top);
    }];
    
    NSIndexPath *indexPath=[NSIndexPath indexPathForRow:0 inSection:0];
   [_xxpk_tableView selectRowAtIndexPath:indexPath animated:NO scrollPosition:UITableViewScrollPositionNone];
   NSIndexPath *path=[NSIndexPath indexPathForItem:0 inSection:0];
   [self tableView:_xxpk_tableView didSelectRowAtIndexPath:path];
}

- (void)xxpk_payButtonDidClicked:(id)sender {
    [[XXGWindowManager shared] xxpk_dismissWindow];
    if (self.xxpk_delegate && [self.xxpk_delegate respondsToSelector:@selector(xxpk_selectPayMethodPayButtonDidClickOfProductItem:)]) {
        [self.xxpk_delegate xxpk_selectPayMethodPayButtonDidClickOfProductItem:self.xxpk_product.xxpk_pay_method[self.xxpk_selectIdx]];
    }
}

- (UIView *)xxpk_headerView {
    CGFloat cellH = 30;
    CGFloat height = [self.xxpk_product.xxpk_amount_text xxpk_isNotEmpty] * cellH + [self.xxpk_product.xxpk_discount_text xxpk_isNotEmpty]  * cellH + 5;
    
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, height)];
    
    CGFloat toTop = 5;
    
    if ([self.xxpk_product.xxpk_amount_text xxpk_isNotEmpty]) {
        UILabel *lNameLabel = [[UILabel alloc] init];
        [headerView addSubview:lNameLabel];
        lNameLabel.text = [NSString stringWithFormat:@"%@:", XXGUIDriver.xxpk_string_ui.xxpk_p_amount_text];
        lNameLabel.font = [UIFont boldSystemFontOfSize:15];
        lNameLabel.textColor = UIColor.darkGrayColor;
        [lNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(toTop);
            make.left.mas_equalTo(12);
            make.height.mas_equalTo(cellH);
        }];
        
        UILabel *rNameLabel = [[UILabel alloc] init];
        [headerView addSubview:rNameLabel];
        rNameLabel.text = self.xxpk_product.xxpk_amount_text;
        rNameLabel.textColor = UIColor.systemOrangeColor;
        [rNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(toTop);
            make.right.mas_equalTo(-10);
            make.height.mas_equalTo(cellH);
        }];
        toTop += cellH;
    }
    
    if ([self.xxpk_product.xxpk_discount_text xxpk_isNotEmpty]) {
        UILabel *lNameLabel = [[UILabel alloc] init];
        [headerView addSubview:lNameLabel];
        lNameLabel.text = [NSString stringWithFormat:@"%@:", XXGUIDriver.xxpk_string_ui.xxpk_p_discount_text];
        lNameLabel.font = [UIFont boldSystemFontOfSize:15];
        lNameLabel.textColor = UIColor.darkGrayColor;
        [lNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(toTop);
            make.left.mas_equalTo(12);
            make.height.mas_equalTo(cellH);
        }];
        
        UILabel *rNameLabel = [[UILabel alloc] init];
        [headerView addSubview:rNameLabel];
        rNameLabel.text = self.xxpk_product.xxpk_discount_text;
        rNameLabel.textColor = UIColor.systemOrangeColor;
        [rNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(toTop);
            make.right.mas_equalTo(-10);
            make.height.mas_equalTo(cellH);
        }];
    }
    
    return headerView;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.xxpk_product.xxpk_pay_method.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    XXGSelectPayCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(XXGSelectPayCell.class) forIndexPath:indexPath];
    cell.xxpk_model = self.xxpk_product.xxpk_pay_method[indexPath.row];;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    if (cell) {
        cell.selected = YES;
        _xxpk_selectIdx = indexPath.row;
    }
}

- (void)tableView:(UITableView *)tableView didDeselectRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    if (cell) {
        cell.selected = NO;
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    if (UIInterfaceOrientationIsPortrait(UIApplication.sharedApplication.statusBarOrientation)) {
#pragma clang diagnostic pop
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float8);
            make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float8);
            make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_ppselecteWidth);
            make.centerY.mas_equalTo(0);
        }];
    }else {
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_ppselecteWidth);
            make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float8);
            make.bottom.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float8);
            make.centerX.mas_equalTo(0);
        }];
    }
}

- (void)xxpk_closeButtonAction:(UIButton *)sender{
    [super xxpk_closeButtonAction:sender];
    if (self.xxpk_delegate && [self.xxpk_delegate respondsToSelector:@selector(xxpk_selectPayMethodCloseButtonDidClick)]) {
        [self.xxpk_delegate xxpk_selectPayMethodCloseButtonDidClick];
    }
}
@end
