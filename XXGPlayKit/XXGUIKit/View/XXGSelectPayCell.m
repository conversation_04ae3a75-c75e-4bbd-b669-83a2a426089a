//
//  XXGSelectPayCell.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/19.
//

#import "XXGSelectPayCell.h"
#import "XXGUIDriver.h"
#import "Masonry.h"
#import "UIImage+XXGImage.h"
#import "UIImageView+WebCache.h"
#import "NSString+XXGString.h"

@interface XXGSelectPayCell()

/// 图像
@property (nonatomic,strong) NSString * xxpk_imageUrl;

/// 图像
@property (nonatomic,strong) UIImageView * xxpk_imageView;

/// p name
@property (nonatomic,strong) UILabel * xxpk_pNameLabel;

/// xxpk_noteLabel
@property (nonatomic,strong) UILabel * xxpk_noteLabel;

@property (nonatomic, strong) UIButton * xxpk_sButton;

@end

@implementation XXGSelectPayCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        
        self.clipsToBounds = YES;
        self.layer.cornerRadius = XXGUIDriver.xxpk_data_ui.xxpk_float4;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        self.xxpk_imageView = [UIImageView new];
        self.xxpk_imageView.tintColor = [XXGUIDriver xxpk_mainColor];
        self.xxpk_imageView.layer.cornerRadius = XXGUIDriver.xxpk_data_ui.xxpk_float35;
        [self.contentView addSubview:self.xxpk_imageView];
        [self.xxpk_imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
            make.centerY.mas_equalTo(self.contentView);
            make.width.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float52);
        }];
        
        self.xxpk_pNameLabel = [UILabel new];
        self.xxpk_pNameLabel.font = [UIFont boldSystemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float16];
        self.xxpk_pNameLabel.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.xxpk_pNameLabel];
        
        self.xxpk_noteLabel = [UILabel new];
        self.xxpk_noteLabel.font = [UIFont boldSystemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float14];
        self.xxpk_noteLabel.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.xxpk_noteLabel];
        
        [self.xxpk_pNameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.xxpk_imageView.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
            make.centerY.equalTo(self.contentView);
        }];
        
        [self.xxpk_noteLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.xxpk_pNameLabel);
            make.top.equalTo(self.xxpk_pNameLabel.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float3);
        }];
        
        self.xxpk_sButton = [[UIButton alloc] init];
        _xxpk_sButton.userInteractionEnabled = NO;
        
        UIImage *image = [[UIImage xxpk_imageBundleOfName:XXGUIDriver.xxpk_data_ui.xxpk_img_sp_cell_ss] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        [_xxpk_sButton setBackgroundImage:[UIImage xxpk_imageBundleOfName:XXGUIDriver.xxpk_data_ui.xxpk_img_sp_cell_ns] forState: UIControlStateNormal];
        [_xxpk_sButton setBackgroundImage:image forState: UIControlStateSelected];
        _xxpk_sButton.tintColor = [XXGUIDriver xxpk_mainColor];
        [self.contentView addSubview:_xxpk_sButton];
        [_xxpk_sButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.offset(0);
            make.right.offset(-XXGUIDriver.xxpk_data_ui.xxpk_float10);
            make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float24, XXGUIDriver.xxpk_data_ui.xxpk_float24));
        }];
    }
    return self;
}

- (void)setSelected:(BOOL)selected {
    _xxpk_sButton.selected = selected;
    self.layer.borderWidth = selected ? 1:0;
    self.layer.borderColor = [XXGUIDriver xxpk_mainColor].CGColor;
}

- (void)setFrame:(CGRect)frame {
    frame.origin.x = XXGUIDriver.xxpk_data_ui.xxpk_float8;
    frame.size.width -= XXGUIDriver.xxpk_data_ui.xxpk_float16;
    frame.origin.y += XXGUIDriver.xxpk_data_ui.xxpk_float8;
    frame.size.height -= XXGUIDriver.xxpk_data_ui.xxpk_float8;
    [super setFrame:frame];
}

-(void)setXxpk_imageUrl:(NSString *)xxpk_imageUrl {
    _xxpk_imageUrl = xxpk_imageUrl;
    [self.xxpk_imageView sd_setImageWithURL:[NSURL URLWithString:xxpk_imageUrl] placeholderImage:nil];
}


- (void)setXxpk_model:(XXGSelectProductItem *)xxpk_model {
    _xxpk_model= xxpk_model;
    self.xxpk_imageUrl = xxpk_model.xxpk_logo;
    self.xxpk_pNameLabel.text = xxpk_model.xxpk_name;
    NSString *note = xxpk_model.xxpk_note?:@"";
    if (note.xxpk_isEmpty) {
        self.xxpk_noteLabel.hidden = YES;
        [self.xxpk_pNameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.xxpk_imageView.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
            make.centerY.equalTo(self.contentView);
        }];
    }else {
        self.xxpk_noteLabel.hidden = NO;
        NSRange range1 = [note rangeOfString:XXGUIDriver.xxpk_data_ui.xxpk_p_selcitem_note_b];
        NSRange range2 = [note rangeOfString:XXGUIDriver.xxpk_data_ui.xxpk_p_selcitem_note_bb];
        
        if (range1.length == 0 && range2.length == 0) {
            self.xxpk_noteLabel.text = note;
            self.xxpk_noteLabel.font = [UIFont systemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float14];
            self.xxpk_noteLabel.textColor = UIColor.lightGrayColor;
        }else {
            NSRange numRange = NSMakeRange(range1.location+range1.length, range2.location-(range1.location+range1.length));
            NSString *numStr = [note substringWithRange:numRange];
            NSString *showStr = [note stringByReplacingOccurrencesOfString:XXGUIDriver.xxpk_data_ui.xxpk_p_selcitem_note_b withString:@""];
            showStr = [showStr stringByReplacingOccurrencesOfString:XXGUIDriver.xxpk_data_ui.xxpk_p_selcitem_note_bb withString:@""];
            
            numRange = [showStr rangeOfString:numStr];
            NSMutableAttributedString *attributeStr = [[NSMutableAttributedString alloc] initWithString:showStr];
            [attributeStr addAttribute:NSForegroundColorAttributeName value:[UIColor lightGrayColor] range:NSMakeRange(0, showStr.length)];
            [attributeStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:14] range:NSMakeRange(0, showStr.length)];
            [attributeStr addAttribute:NSForegroundColorAttributeName value:[XXGUIDriver xxpk_mainColor] range:numRange];
            [attributeStr addAttribute:NSFontAttributeName value:[UIFont boldSystemFontOfSize:14] range:numRange];
            
            self.xxpk_noteLabel.attributedText = attributeStr;
        }
        
        [self.xxpk_pNameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.xxpk_imageView.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
            make.top.equalTo(self.xxpk_imageView).offset(XXGUIDriver.xxpk_data_ui.xxpk_float5);
        }];
    }
}

@end
