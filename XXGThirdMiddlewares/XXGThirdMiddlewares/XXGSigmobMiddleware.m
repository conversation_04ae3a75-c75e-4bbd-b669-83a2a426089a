//
//  XXGWechatOpenSDKMiddleware.m
//  XXGWechatOpenSDKMiddleware
//
//  Created by apple on 2025/6/19.
//  国内广告 https://doc.sigmob.com/

#import <Foundation/Foundation.h>
#import <WindSDK/WindSDK.h>
#import <WindFoundation/WindFoundation.h>

@interface XXGSigmobMiddleware : NSObject<WindRewardVideoAdDelegate>

@property (nonatomic, strong) WindRewardVideoAd *rewardVideoAd;

@property (nonatomic, copy) void (^xxpk_complate)(BOOL result);

@property (nonatomic, strong) WindAdRequest *request;

@end

@implementation XXGSigmobMiddleware

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

- (void)xxpk_sigmobInitWithAppId:(NSString *)appId appKey:(NSString *)appKey {  // SDK初始化接口
    WindAdOptions *option = [[WindAdOptions alloc] initWithAppId:@"6877" appKey:@"eccdcdbd9adbd4a7"];
    [WindAds startWithOptions:option];
}

- (void)xxpk_sigmoLoadAdWithPlacementId:(NSString *)placementId uid:(NSString *)uid options:(NSDictionary *)options {
    WindAdRequest *request = [WindAdRequest request];
    request.userId = @"your user id";
    request.placementId = @"ea1f8f7b662";
    request.options = @{@"test_key":@"test_value"};
    self.request = request;
    [self __xxpk_sigmoLoadAdWithRequest:request];
}

- (void)__xxpk_sigmoLoadAdWithRequest:(WindAdRequest *)request {
    if (!self.rewardVideoAd) {
        self.rewardVideoAd = [[WindRewardVideoAd alloc] initWithRequest:request];
    }
    self.rewardVideoAd.delegate = self;
    [self.rewardVideoAd loadAdData];
}

- (void)xxpk_sigmobShowAdFromRootViewController:(UIViewController *)rootViewController complate:(void(^)(BOOL result))complate {
    self.xxpk_complate = complate;
    if (!self.rewardVideoAd.isAdReady) {
        // not ready!
        if (complate) {
            complate(NO);
        }
        return;
    }
    // 当多场景使用同一个广告位是，可以通过WindAdSceneId来区分某个场景的广告播放数据
    // 不需要统计可以设置为options=nil
    [self.rewardVideoAd showAdFromRootViewController:rootViewController options:@{
        WindAdSceneDesc: @"测试场景",
        WindAdSceneId: @"1"
    }];
}

#pragma mark - WindRewardVideoAdDelegate
- (void)rewardVideoAdDidLoad:(WindRewardVideoAd *)rewardVideoAd {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
}

- (void)rewardVideoAdDidLoad:(WindRewardVideoAd *)rewardVideoAd didFailWithError:(NSError *)error {
    NSLog(@"%@ -- %@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId, error.localizedDescription);
    self.xxpk_complate(NO);
    if (error) {
        [self __xxpk_sigmoLoadAdWithRequest:self.request];
    }
}

- (void)rewardVideoAdWillVisible:(WindRewardVideoAd *)rewardVideoAd {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
}

- (void)rewardVideoAdDidVisible:(WindRewardVideoAd *)rewardVideoAd {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
}

- (void)rewardVideoAdDidClick:(WindRewardVideoAd *)rewardVideoAd {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
}

- (void)rewardVideoAdDidClickSkip:(WindRewardVideoAd *)rewardVideoAd {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
}

- (void)rewardVideoAd:(WindRewardVideoAd *)rewardVideoAd reward:(WindRewardInfo *)reward {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
    self.xxpk_complate(YES);
}

- (void)rewardVideoAdDidClose:(WindRewardVideoAd *)rewardVideoAd {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
}

- (void)rewardVideoAdDidPlayFinish:(WindRewardVideoAd *)rewardVideoAd didFailWithError:(NSError *)error {
    NSLog(@"%@ -- %@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId, error.localizedDescription);
    self.xxpk_complate(NO);
    [self __xxpk_sigmoLoadAdWithRequest:self.request];
}

/**
 This method is called when return ads from sigmob ad server.
 */
- (void)rewardVideoAdServerResponse:(WindRewardVideoAd *)rewardVideoAd isFillAd:(BOOL)isFillAd {
    NSLog(@"%@ -- %@", NSStringFromSelector(_cmd), rewardVideoAd.placementId);
}

- (void)dealloc {
    self.rewardVideoAd.delegate = nil;
    self.rewardVideoAd = nil;
}

@end
