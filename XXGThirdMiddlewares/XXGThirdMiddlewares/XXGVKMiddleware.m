//
//  XXGVKMiddleware.m
//
//  Created by apple on 2025/3/26.
//  对接文档 新版：https://id.vk.com/about/business/go/docs/ru/vkid/latest/vk-id/connection/start-integration/ios/install
//  对接文档 旧版：https://dev.vk.com/en/sdk/ios

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <VKID/VKID-Swift.h>

@interface XXGVKMiddleware : NSObject

@end

@implementation XXGVKMiddleware

+ (void)xxpk_oauthOnViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler {
    [[VKIDExtension shared] oauthWithPresentingController:vc completion:^(BOOL isCancell, NSString * userId, NSString * token, NSString * error) {
        if (isCancell) {
            handler(YES,@"",@"",@"");
        }else {
            handler(NO,userId,token,error);
        }
    }];
}

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [[VKIDExtension shared] handleOpenURL:url];
    return YES;
}

+ (void)xxpk_startVKWithClientID:(NSString *)clientId clientSecret:(NSString *)clientSecret{
    [[VKIDExtension shared] initvkWithClientId:clientId clientSecret:clientSecret];
}

// club230660342
+ (void)xxpk_jumpToVKAndFollw:(NSString *)vkhome {
    NSURL *vkURL = [NSURL URLWithString:[NSString stringWithFormat:@"vk://vk.com/club%@",vkhome]];
    
    if (![[UIApplication sharedApplication] canOpenURL:vkURL]) {
        vkURL = [NSURL URLWithString:[NSString stringWithFormat:@"https://www.vk.com/club%@",vkhome]];
    }
    [[UIApplication sharedApplication] openURL:vkURL options:@{} completionHandler:nil];
}

@end
