//
//  XXGFacebookMiddleware.m
//
//  Created by apple on 2025/3/21.
//  登录文档：https://developers.facebook.com/docs/ios/
//  事件文档：https://developers.facebook.com/docs/app-events/getting-started-app-events-ios
//  https://github.com/facebook/facebook-ios-sdk/releases
//  下载 FacebookSDK-Static_XCFramework.zip

#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import <FBSDKLoginKit/FBSDKLoginKit.h>
#import <FBSDKShareKit/FBSDKShareKit.h>
#import <FBSDKGamingServicesKit/FBSDKGamingServicesKit-Swift.h>

@interface XXGFacebookMiddleware : NSObject

@end

@implementation XXGFacebookMiddleware

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (void)load {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(__xxpk_didBecomeActiveNotification) name:UIApplicationDidBecomeActiveNotification object:nil];
}

+ (void)__xxpk_didBecomeActiveNotification  {
    [[FBSDKAppEvents shared] activateApp];
}

+ (NSString *)xxpk_version {
    return FBSDKSettings.sharedSettings.sdkVersion;
}

+ (void)xxpk_application:(UIApplication * _Nonnull)application xdidFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions {
    [[FBSDKApplicationDelegate sharedInstance] application:application didFinishLaunchingWithOptions:launchOptions];
    FBSDKSettings.sharedSettings.isAutoLogAppEventsEnabled = YES;
    FBSDKSettings.sharedSettings.isAdvertiserIDCollectionEnabled = YES;
    FBSDKProfile.isUpdatedWithAccessTokenChange = YES;
}

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    return [[FBSDKApplicationDelegate sharedInstance] application:application openURL:url options:options];
}

+ (void)xxpk_oauth:(UIViewController *)vc handler:(void(^)(NSString *userID, NSString*name, NSString*token,NSString *auth_token,NSString *nonce, NSError*error, BOOL isCancelled))handler {
    FBSDKLoginManager *login = [[FBSDKLoginManager alloc] init];
    [login logOut];
    [login logInWithPermissions:@[@"public_profile"] fromViewController:vc handler:^(FBSDKLoginManagerLoginResult *_Nullable result, NSError *_Nullable error) {
        if (error) {
            handler(nil,nil,nil,nil,nil,error,NO);
        } else if (result.isCancelled) {
            handler(nil,nil,nil,nil,nil,nil,YES);
        } else {
            NSString *userID = result.token.userID;
            NSString *name = [FBSDKProfile currentProfile].name;
            NSString *tokenStr = result.token.tokenString;
            NSString *auth_token = result.authenticationToken.tokenString;
            NSString *nonce = result.authenticationToken.nonce;
            handler(userID,name,tokenStr,auth_token,nonce,error,NO);
        }
    }];
}

// 获取Id方式打开网页html搜索message_box_id对应的id就是fbhome
// 例如："message_box_id":"102371258518280" 或者 null,"id":"61577284438784"
+ (void)xxpk_jumpToFacebookAndFollw:(NSString *)fbhome {
    NSURL *facebookURL = [NSURL URLWithString:[NSString stringWithFormat:@"fb://profile/%@",fbhome]];
    
    if (![[UIApplication sharedApplication] canOpenURL:facebookURL]) {
        facebookURL = [NSURL URLWithString:[NSString stringWithFormat:@"https://www.facebook.com/%@",fbhome]];
    }
    [[UIApplication sharedApplication] openURL:facebookURL options:@{} completionHandler:nil];
}

/// 邀请好友一起游戏
+ (void)xxpk_launchFriendFinderDialogWithCompletionHandler:(void(^)(BOOL success, NSError * _Nullable error))completionHandler {
    [FBSDKFriendFinderDialog launchFriendFinderDialogWithCompletionHandler:completionHandler];
}

+ (void)xxpk_logViewedContentEvent {
    [FBSDKAppEvents.shared logEvent:FBSDKAppEventNameViewedContent];
}

+ (void)xxpk_logCompletedRegistrationEvent {
    [FBSDKAppEvents.shared logEvent:FBSDKAppEventNameCompletedRegistration];
}

+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid {
    
   NSDictionary *params = [[NSDictionary alloc] initWithObjectsAndKeys:
                           uid, FBSDKAppEventParameterNameContentID,
                           nil];
    
    [FBSDKAppEvents.shared logEvent:event parameters:params];
}

+ (void)xxpk_logPurchasedEventOrderId :(NSString*)xxpk_orderId
                        currency:(NSString*)currency
                                price :(double)price {
   NSDictionary *params = [[NSDictionary alloc] initWithObjectsAndKeys:
                           @"orderId", FBSDKAppEventParameterNameContentType,
                           xxpk_orderId, FBSDKAppEventParameterNameContentID,
                           currency, FBSDKAppEventParameterNameCurrency,
                           nil];

    [FBSDKAppEvents.shared logPurchase:price
                      currency: currency
                    parameters: params];
}

+ (void)xxpk_fbUniversalLogEvent:(FBSDKAppEventName)eventName withUid:(NSString *)uid params:(NSDictionary *)params {
    NSMutableDictionary *mutParams = [[NSMutableDictionary alloc] initWithDictionary:@{@"uid":uid}];
    if (params) {
        [mutParams addEntriesFromDictionary:params];
    }
    [FBSDKAppEvents.shared logEvent:eventName parameters:mutParams];
}

+ (void)xxpk_sharedLinkToFacebookWithUrl:(NSString *)linkUrl withvc:(UIViewController *)vc {
    [self xxpk_sharedToFacebook:0 url:linkUrl image:nil withvc:vc];
}

+ (void)xxpk_sharedImageToFacebookWithImage:(UIImage *)image  withvc:(UIViewController *)vc {
    [self xxpk_sharedToFacebook:1 url:nil image:image withvc:vc];
}

+ (void)xxpk_sharedImageToFacebookWithImageUrl:(NSString *)imageUrl  withvc:(UIViewController *)vc {
    [self xxpk_sharedToFacebook:1 url:imageUrl image:nil withvc:vc];
}

+ (void)xxpk_sharedToFacebook:(int)type url:(NSString *)url image:(UIImage *)image withvc:(UIViewController *)vc {
    
    if (type == 0) {
        FBSDKShareLinkContent *link_content = [[FBSDKShareLinkContent alloc] init];
        link_content.contentURL = [NSURL URLWithString:url];
        FBSDKShareDialog *dialog = [FBSDKShareDialog dialogWithViewController:vc withContent:link_content delegate:nil];
        dialog.mode = FBSDKShareDialogModeNative;
        [dialog show];
    }
    
    if (type == 1) {
        if (image) {
            // 图片
            FBSDKSharePhoto *photo = [[FBSDKSharePhoto alloc] initWithImage:image isUserGenerated:NO];
            FBSDKSharePhotoContent *img_content = [[FBSDKSharePhotoContent alloc] init];
            img_content.photos = @[photo];
            FBSDKShareDialog *dialog = [FBSDKShareDialog dialogWithViewController:vc withContent:img_content delegate:nil];
            dialog.mode = FBSDKShareDialogModeNative;
            [dialog show];
        }else {
            [self xxpk_downloadImageWithURL:url completion:^(UIImage *image, NSError *error) {
                if (error) {
                    NSLog(@"下载失败: %@", error.localizedDescription);
                    return;
                }
                
                if (image) {
                    FBSDKSharePhoto *photo = [[FBSDKSharePhoto alloc] initWithImage:image isUserGenerated:NO];
                    FBSDKSharePhotoContent *img_content = [[FBSDKSharePhotoContent alloc] init];
                    img_content.photos = @[photo];
                    FBSDKShareDialog *dialog = [FBSDKShareDialog dialogWithViewController:vc withContent:img_content delegate:nil];
                    dialog.mode = FBSDKShareDialogModeNative;
                    [dialog show];
                }
            }];
        }
    }
}

+ (void)xxpk_downloadImageWithURL:(NSString *)urlString completion:(void (^)(UIImage *image, NSError *error))completion {
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"ImageDownloadErrorDomain"
                                                code:-1
                                            userInfo:@{NSLocalizedDescriptionKey : @"Invalid URL"}];
            completion(nil, error);
        }
        return;
    }
    
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];
    
    NSURLSessionDataTask *task = [session dataTaskWithURL:url completionHandler:^(NSData * _Nullable data,
                                                                                  NSURLResponse * _Nullable response,
                                                                                  NSError * _Nullable error) {
        // 处理错误
        if (error) {
            [self xxpk_safeCallCompletion:completion image:nil error:error];
            return;
        }
        
        // 验证HTTP状态码
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        if (httpResponse.statusCode != 200) {
            NSError *statusError = [NSError errorWithDomain:@"ImageDownloadErrorDomain"
                                                       code:httpResponse.statusCode
                                                   userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTP %ld", (long)httpResponse.statusCode]}];
            [self xxpk_safeCallCompletion:completion image:nil error:statusError];
            return;
        }
        
        // 转换图片数据
        UIImage *image = [UIImage imageWithData:data];
        if (!image) {
            NSError *imageError = [NSError errorWithDomain:@"ImageDownloadErrorDomain"
                                                      code:-2
                                                  userInfo:@{NSLocalizedDescriptionKey : @"Failed to decode image data"}];
            [self xxpk_safeCallCompletion:completion image:nil error:imageError];
            return;
        }
        
        [self xxpk_safeCallCompletion:completion image:image error:nil];
    }];
    
    [task resume];
}

// 确保在主线程回调
+ (void)xxpk_safeCallCompletion:(void (^)(UIImage *, NSError *))completion
                    image:(UIImage *)image
                    error:(NSError *)error {
    if (!completion) return;
    
    if ([NSThread isMainThread]) {
        completion(image, error);
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(image, error);
        });
    }
}
@end
