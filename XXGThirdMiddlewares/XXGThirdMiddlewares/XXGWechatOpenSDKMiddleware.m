//
//  XXGWechatOpenSDKMiddleware.m
//  XXGWechatOpenSDKMiddleware
//
//  Created by apple on 2025/6/19.
//  https://developers.weixin.qq.com/doc/oplatform/open/intro.html

#import <Foundation/Foundation.h>
#import <WechatOpenSDK/WXApi.h>

@interface XXGWechatOpenSDKMiddleware : NSObject<WXApiDelegate>

@end

@implementation XXGWechatOpenSDKMiddleware

+ (void)xxpk_sendReqWithQuery:(NSString *)query {
    WXOpenBusinessViewReq *req = [WXOpenBusinessViewReq object];
    req.businessType = @"requestMerchantTransfer";
    req.query = query;
    [WXApi sendReq:req completion:^(BOOL success) {
        NSLog(@"xxpk_sendReqWithQuery:%d",success);
    }];
}

+ (void)xxpk_registerApp:(NSString *)wxAppId universalLink:(NSString *)universalLink {
    BOOL result = [WXApi registerApp:wxAppId universalLink:universalLink];
    NSLog(@"微信SDK注册%@,appid:%@", result?@"成功":@"失败", wxAppId);
}

+ (void)xxpk_handleOpenUniversalLink:(NSUserActivity *)userActivity {
    [WXApi handleOpenUniversalLink:userActivity delegate:nil];
}

@end
