---
Language: ObjC
BasedOnStyle: LLVM

# 基本缩进设置
IndentWidth: 4
TabWidth: 4
UseTab: Never
ContinuationIndentWidth: 4

# 大括号风格
BreakBeforeBraces: Allman

# 行长度设置
ColumnLimit: 120

# Objective-C 特定设置
ObjCBlockIndentWidth: 4
ObjCBreakBeforeNestedBlockParam: false
ObjCSpaceAfterProperty: false
ObjCSpaceBeforeProtocolList: true

# 参数和方法设置 - 强制同行
BinPackParameters: true
BinPackArguments: true
AllowAllParametersOfDeclarationOnNextLine: false
AllowAllArgumentsOnNextLine: false
ExperimentalAutoDetectBinPacking: false

# 换行设置
AllowShortBlocksOnASingleLine: Empty
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: None
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false

# 二元操作符设置
BreakBeforeBinaryOperators: None
AlignOperands: true

# 空格设置
SpaceAfterCStyleCast: false
SpaceBeforeAssignmentOperators: true
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: false
SpacesInParentheses: false
SpacesInSquareBrackets: false

# 指针对齐
PointerAlignment: Right

# 其他设置
SortIncludes: Never
KeepEmptyLinesAtTheStartOfBlocks: false
MaxEmptyLinesToKeep: 1
IndentCaseLabels: false

# 惩罚设置 - 阻止不必要的换行
PenaltyBreakBeforeFirstCallParameter: 500
PenaltyBreakAssignment: 100
PenaltyBreakComment: 300
PenaltyBreakString: 1000
PenaltyExcessCharacter: 1000000
