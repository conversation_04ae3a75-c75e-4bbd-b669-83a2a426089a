/*********************************
 *********************************
 ***当前SDK接口对接演示，请结合文档对接
 *********************************
 *********************************
 **/
#import "AppDelegate.h"
#import <XXGPlayKitCN/XXGPlayKitCN.h>

@interface AppDelegate ()

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
#ifdef XXGPLAYKIT_DEBUG
    // 此接口为内部测试使用请勿调用
    // 微信登录
//    [XXGSetting xxpk_setTestAppId:@"" bundleId:@"com.xxhh.xnjz" appVersion:@"1"];
    
    // MARK: - 控制SDK关闭按钮是否隐藏
    // 隐藏SDK界面右上角关闭按钮默认为：YES 隐藏状态
    // 如果游戏登录界面没有登录按钮用来拉起SDK登录界面，不用调用或设置为YES，以免用户关闭后无法重新拉起登录界面
    [XXGSetting xxpk_setCloseButtonHidden:NO];
#endif
    
    // MARK: - 启动（必接）
    // 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现
    [XXGPlayCN xxpk_didFinishLaunchingWithOptions:launchOptions xconnectOptions:nil];
    
    return YES;
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    
    // MARK: - openURL（必接）
    // 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现
    [XXGPlayCN xxpk_applicationOpenURL:url xoptions:options xURLContexts:nil];
    return YES;
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
    
    // MARK: - 通用链接（必接）
    // 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现
    [XXGPlayCN xxpk_applicationContinueUserActivity:userActivity];
    
    return YES;
}

#pragma mark - UISceneSession lifecycle

- (UISceneConfiguration *)application:(UIApplication *)application configurationForConnectingSceneSession:(UISceneSession *)connectingSceneSession options:(UISceneConnectionOptions *)options {
    // Called when a new scene session is being created.
    // Use this method to select a configuration to create the new scene with.
    return [[UISceneConfiguration alloc] initWithName:@"Default Configuration" sessionRole:connectingSceneSession.role];
}


- (void)application:(UIApplication *)application didDiscardSceneSessions:(NSSet<UISceneSession *> *)sceneSessions {
    // Called when the user discards a scene session.
    // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
    // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
}


@end
