/*********************************
 *********************************
 ***当前SDK接口对接演示，请结合文档对接
 *********************************
 *********************************
 **/
#import "_.h"
#import "ViewController.h"
#import <XXGPlayKitCN/XXGPlayKitCN.h>

// MARK: - 遵循协议（必接）
@interface ViewController ()<XXGPlayDelegate>

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // MARK: - 设置各个接口回调代理（必接）
    // 设置代理用于接收登录、退出、支付、上报角色结果
    [XXGPlayCN xxpk_setPlayDelegate:self];
}

//MARK: - 登录（必接）
- (IBAction)demo_login:(id)sender {
    [self demo_log:@"点击登录"];
    // 登录接口
    [XXGPlayCN xxpk_comein];
}

//MARK: - 退出（必接）
- (IBAction)demo_logout:(id)sender {
    [self demo_log:@"点击退出"];
    // 退出接口
    [XXGPlayCN xxpk_logout];
}

//MARK: - 支付（必接）
- (IBAction)demo_pay:(id)sender {
    [self demo_log:@"点击支付"];
    /**
     * 订单模型
     * 注意：确保传进的参数都为NSString类型，不接受NSNumber类型
     */
    NSString *cpOrderId = NSUUID.UUID.UUIDString;       // 游戏生成的订单号 (必传)
    NSString *productCode = @"com.xxgame.sdk.demo.228"; // 商品标识(苹果商品标识)(必传)
    NSString *amount = @"228";                          // 商品金额（单位：元） (必传)
    NSString *productName = @"SDK测试商品";               // 商品名称、例：60元宝 (必传)
    NSString *serverId = @"20190927001";                // 用户游戏角色所在的服务器id (必传)
    NSString *roleId = @"100001";                       // 用户游戏角色ID (必传)
    NSString *roleName = @"角色-XXGameSDK";              // 用户游戏角色名称 (必传)
    NSString *roleLevel = @"99";                         // 用户游戏角色等级 (必传)
    NSString *extraInfo = @"2019";                       // 订单额外信息，最终将回传给游戏服务器 (选传)
    
    /**
     * 支付,传入订单模型
     * 支付结果通过代理方法xxpk_payFinished:返回 YES为成功，NO为失败
     */
    [XXGPlayCN xxpk_createOrder:cpOrderId
             xxpk_productCode:productCode
                  xxpk_amount:amount
             xxpk_productName:productName
                xxpk_serverId:serverId
               xxpk_extraInfo:extraInfo
                  xxpk_roleId:roleId
                xxpk_roleName:roleName
               xxpk_roleLevel:roleLevel];
}

//MARK: - 上报角色（必接）
- (IBAction)demo_uploadRoleInfo:(id)sender {
    [self demo_log:@"点击上报角色"];
    /**
     * 必接！！！
     * 注意：确保传进的参数都为NSString类型，不接受NSNumber类型，extend字段类型为字典
     * 调用时机(重要!!只在以下两个场景需调用):
     * - 玩家在选择区服进入游戏时调用该接口。
     * - 角色升级或其他角色汇报信息发生变化时调用该接
     */
    // 区服id (必传)
    NSString *serverId = @"20190927001";
    // 区服名称 (必传)
    NSString *serverName = @"XXGame";
    // 用户游戏角色ID (必传)
    NSString *roleId = @"100001";
    // 角色名称 (必传)
    NSString *roleName = @"角色-XXGameSDK";
    // 角色等级 (必传)
    NSString *roleLevel = @"15";
    // 角色扩展信息（选填）类型：字典
    NSDictionary *extend = @{
        @"pet": @"60",          //宠物等级（5）
        @"horse": @"15",        //坐骑等级（1）
        @"power": @"10000",        //战力（100）
        @"promote": @"2",      //转职（2转）
        @"married": @"0",      //'0': 未婚, '1': 已婚
        @"liveness": @"2000",    //活跃度 (2000)
        @"hero_level": @"98",   //英雄等级(98级)
        @"guanqia": @"2",       // 关卡
        @"trumps": @[ //已激活的法宝列表
             @"fabao1",   //法宝1
             @"fabao2"    //法宝2
        ],
        @"wings": @[  //已激活的翅膀列表
             @"wing1",    //翅膀1
             @"wing2"     //翅膀2
        ],
        @"artifacts": @[  //已激活的神器列表
             @"artifact1",    //神器1
             @"artifact2",    //神器2
        ],
        //@"xxx":@"xxx"                     //其他自定义信息
        //@"xxx":@"xxx"                     //其他自定义信息
        //@"xxx":@"xxx"                     //其他自定义信息
        //...
    };
    
    /**上报角色信息*/
    [XXGPlayCN xxpk_uploadRoleInfo:serverId
                   xxpk_serverName:serverName
                       xxpk_roleId:roleId
                     xxpk_roleName:roleName
                    xxpk_roleLevel:roleLevel
                       xxpk_extend:extend];
}

//MARK: - 打开个人中心
- (IBAction)demo_openUserCenter:(id)sender {
    [self demo_log:@"点击打开个人中心"];
    /*
     @param type  指定打开默认展示页面；NSString类型
        Red -  红包
        My - 我的
        Gift - 礼包
        Share - 分享
        Activity - 活动
     */
    [XXGPlayCN xxpk_openUserCenterSidebar:@"My"];
}

//MARK: - 激励广告
- (IBAction)demo_rewardAd:(id)sender {
    [self demo_log:@"点击激励广告"];
    [XXGPlayCN xxpk_sigmobShowAdComplate:^(BOOL result) {
        [self demo_log:[NSString stringWithFormat:@"广告获得激励-%@", result?@"成功":@"失败"]];
    }];
}

//MARK: - 苹果内购修复
- (IBAction)demo_iapRepair:(id)sender {
    [self demo_log:@"点击苹果内购修复"];
    [XXGPlayCN xxpk_iapRepair];
}

// MARK: - XXGPlayDelegate
// MARK: 登录回调（必接）
- (void)xxpk_comeinFinish:(NSDictionary *)xxpk_box {
    [self demo_log:[NSString stringWithFormat:@"登录回调-登录成功 - %@", xxpk_box.description]];
}

// MARK: 退出回调（必接）
// 必接 - 对接方需在此回调中调用游戏切换账号接口,退到游戏【登录界面】
// SDK个人中心有切换账号功能，所以用户在个人中心操作切换账号需游戏一并退出
// 另外游戏内退出同时调用SDK的logout:函数也会走此回调，
// 所以建议游戏的退出接口直接调用SDK的logout:在此回调中一并退出，不然游戏会重复退出2次
- (void)xxpk_logouted {
    [self demo_log:@"退出回调-SDK已登录出"];
}

// MARK: 支付回调
- (void)xxpk_payFinished:(BOOL)isSuc {
    [self demo_log:[NSString stringWithFormat:@"支付回调-支付%@", isSuc?@"成功":@"失败"]];
}

// MARK: 上报角色回调
- (void)xxpk_uploadRoleFinished:(BOOL)isSuc {
    [self demo_log:[NSString stringWithFormat:@"上报角色回调-上报角色%@", isSuc?@"成功":@"失败"]];
}

@end
