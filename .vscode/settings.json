{
    // 基本编辑器设置
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,

    // Objective-C 格式化器
    "[objective-c]": {
        "editor.defaultFormatter": "llvm-vs-code-extensions.vscode-clangd",
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false
    },

    "[c]": {
        "editor.defaultFormatter": "llvm-vs-code-extensions.vscode-clangd",
        "editor.tabSize": 4
    },

    // 文件类型关联
    "files.associations": {
        "*.h": "objective-c",
        "*.m": "objective-c",
        "*.mm": "objective-cpp"
    },

    // 文件保存设置
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true
}