{"xxpk_startid": "352e8711bcca025e07230a8402f03d09", "xxpk_version": "3.2.7", "xxpk_error_code": "%@ code:%ld", "xxpk_base_url_k": "base_url_value", "xxpk_base_os_url": "api.xxgameos.com", "xxpk_base_cn_url": "api.xxgame.cn,api.xxbox.cn,api.xxtop.cn,api.xxyx.cn", "xxpk_base_fix": "https://%@/kds/v1/init", "xxpk_action": "action", "xxpk_actions": "actions", "xxpk_device": "device", "xxpk_extend": "extend", "xxpk_api_list": "api_list", "xxpk_app": "app", "xxpk_secret": "secret", "xxpk_id": "id", "xxpk_config": "config", "xxpk_adaption": "adaption", "xxpk_skin": "skin", "xxpk_login": "login", "xxpk_extra_params_str": "extra_params", "xxpk_server": "server", "xxpk_timestamp": "timestamp", "xxpk_box": "user", "xxpk_token": "token", "xxpk_sign": "?sign=%@", "xxpk_gzip": "gzip", "xxpk_content_encoding": "Content-Encoding", "xxpk_application_json": "application/json; charset=utf-8", "xxpk_content_type": "Content-Type", "xxpk_http_method": "POST", "xxpk_status": "status", "xxpk_redirect": "redirect", "xxpk_error": "error", "xxpk_network": "XXGNetwork", "xxpk_errmsg": "errmsg", "xxpk_ok": "ok", "xxpk_tip": "tip", "xxpk_recomein": "relogin", "xxpk_url": "url", "xxpk_imgUrl": "imgUrl", "xxpk_boxName": "username", "xxpk_name": "name", "xxpk_boxKey": "password", "xxpk_mobile": "mobile", "xxpk_purpose": "purpose", "xxpk_dial_code": "dial_code", "xxpk_platform": "10002", "xxpk_campaign": "0", "xxpk_ios": "iOS", "xxpk_lang": "AppleLanguages", "xxpk_uniqueId": "uniqueId", "xxpk_open_close": "open_close", "xxpk_close": "close", "xxpk_update": "update", "xxpk_trampoline": "trampoline", "xxpk_splash": "splash", "xxpk_real_name": "real_name", "xxpk_subscribe": "subscribe", "xxpk_sms_code": "sms_code", "xxpk_new_key": "new_password", "xxpk_old_key": "old_password", "xxpk_order": "order", "xxpk_currency": "currency", "xxpk_pm": "pay_method", "xxpk_iap": "iap", "xxpk_h5": "h5", "xxpk_poopo_p": "poopopay", "xxpk_p_status": "pay_status", "xxpk_p_coupon_id": "%@coupon_id=%@", "xxpk_payload": "payload", "xxpk_state": "state", "xxpk_user_info_url": "user_info_url", "xxpk_landscape1": "1", "xxpk_landscape2": "2", "xxpk_net_code_error": "-30003", "xxpk_open": "open", "xxpk_uid": "uid", "xxpk_boxm_boxs_key": "boxm_boxs_value", "xxpk_boxm_comeinedbox_key": "boxm_comeinedbox_value", "xxpk_core_open_page": "&open_page=%@", "xxpk_type": "type", "xxpk_type_shanyan": "<PERSON><PERSON>yan", "xxpk_created": "created", "xxpk_cp_extra": "cp_extra", "xxpk_data": "data", "xxpk_content": "content", "xxpk_scheme_wx": "wx", "xxpk_wx_oauth": "://oauth", "xxpk_weixin": "weixin", "xxpk_open_weixin_auth": "weixin://app/%@/auth/?scope=snsapi_userinfo&state=wxAuth", "xxpk_log_config_success": "日志配置完成", "xxpk_log_resource_load_begin": "开始加载资源", "xxpk_log_resource_load_success": "资源加载完成", "xxpk_log_init_start": "开始初始化", "xxpk_log_init_already": "已经/正在初始化-(%ld)", "xxpk_log_init_success": "初始化完成", "xxpk_log_init_failed": "初始化失败,%@", "xxpk_log_init_login": "初始化完成，检测调用过登录接口，现在去登录", "xxpk_log_login_ing": "正在登录中...", "xxpk_log_login_logined": "已登录，返回用户信息", "xxpk_log_login_prepare": "准备登录", "xxpk_log_login_not_init": "未完成初始化", "xxpk_log_login_start": "开始登录", "xxpk_log_login_success": "登录成功", "xxpk_log_logout": "退出登录", "xxpk_log_report_role": "上报角色信息", "xxpk_log_report_role_success": "上报角色信息成功", "xxpk_log_report_role_failed": "上报角色信息失败", "xxpk_log_pay_start": "拉起支付", "xxpk_log_pay_success": "支付成功", "xxpk_log_pay_failed": "支付失败", "xxpk_log_pay_cancel": "取消支付", "xxpk_log_cold_start_url": "冷启动 URL %@", "xxpk_log_hot_start_url": "热启动 URL %@", "xxpk_log_net_status": "当前网络状态 - %@", "xxpk_log_action_open_close": "Action事件-上报触点", "xxpk_log_action_trampoline": "Action事件-跳转界面: %@", "xxpk_log_action_splash": "Action事件-开场动画: %@", "xxpk_log_action_mobile": "Action事件-绑定手机: %d", "xxpk_log_action_real_name": "Action事件-实名认证: %d", "xxpk_log_action_mqtt": "Action事件-MQTT", "xxpk_log_att_duplicate_request": "[ATT] 已有进程在处理IDFA请求，忽略重复调用", "xxpk_log_att_start_check": "[ATT] 开始IDFA权限检查流程", "xxpk_log_att_current_status": "[ATT] 当前授权状态: %@ (%ld)", "xxpk_log_att_authorized_direct": "[ATT] 已获得追踪权限，直接执行回调", "xxpk_log_att_denied": "[ATT] 用户已拒绝追踪权限，执行回调", "xxpk_log_att_restricted": "[ATT] 设备限制追踪权限，执行回调", "xxpk_log_att_not_determined": "[ATT] 权限未确定，准备在合适时机请求权限", "xxpk_log_att_ios_below_14": "[ATT] iOS版本 < 14.0，无需ATT权限，直接执行回调", "xxpk_log_att_wait_app_active": "[ATT] 注册App激活通知，等待合适时机弹出权限请求", "xxpk_log_att_app_active_delay": "[ATT] App已激活，准备延迟%d秒后请求权限", "xxpk_log_att_delay_app_state": "[ATT] 延迟后App状态: %@", "xxpk_log_att_app_active_request": "[ATT] App处于前台活跃状态，开始请求ATT权限", "xxpk_log_att_app_inactive": "[ATT] App非活跃状态(%@)，原因: 存在系统弹窗｜用户可能已切换到其他应用或锁屏", "xxpk_log_att_add_observer2": "[ATT] 添加新的观察者2号，等待下次活跃再请求ATT权限", "xxpk_log_att_remove_observer2": "[ATT] 已移除App激活通知观察者2号", "xxpk_log_att_app_active_direct": "[ATT] App已激活，直接请求权限", "xxpk_log_att_remove_observer": "[ATT] 已移除App激活通知观察者", "xxpk_log_att_showing_dialog": "[ATT] 正在显示ATT权限弹窗...", "xxpk_log_att_request_complete": "[ATT] 权限请求完成", "xxpk_log_att_callback_status": "[ATT] - 回调状态: %@ (%ld)", "xxpk_log_att_current_actual_status": "[ATT] - 当前实际状态: %@ (%ld)", "xxpk_log_att_authorized_success": "[ATT] ✅ 用户授权成功，可以获取IDFA", "xxpk_log_att_still_not_determined": "[ATT] ⏳ 权限状态仍未确定，启动等待机制", "xxpk_log_att_denied_restricted": "[ATT] ❌ 用户拒绝或限制了追踪权限", "xxpk_log_att_waiting_user": "[ATT] 等待用户授权操作 - 尝试次数: %ld/%ld, 当前状态: %@", "xxpk_log_att_still_waiting": "[ATT] 权限状态仍未确定，1秒后进行第%ld次检查", "xxpk_log_att_timeout": "[ATT] ⏰ 等待超时，已达到最大重试次数(%ld)，结束等待", "xxpk_log_att_timeout_final": "[ATT] 最终状态: %@，可能用户未做选择或系统延迟", "xxpk_log_att_user_choice": "[ATT] 🎯 用户已做出选择，最终状态: %@", "xxpk_log_att_final_authorized": "[ATT] ✅ 用户最终选择了授权", "xxpk_log_att_final_denied": "[ATT] ❌ 用户最终选择了拒绝", "xxpk_log_att_final_restricted": "[ATT] 🚫 系统限制了追踪权限", "xxpk_log_att_wait_end": "[ATT] 等待流程结束，执行最终回调", "xxpk_log_att_ios_below_14_wait": "[ATT] iOS版本 < 14.0，无需等待授权，直接执行回调", "xxpk_att_status_not_determined": "未确定", "xxpk_att_status_restricted": "受限制", "xxpk_att_status_denied": "已拒绝", "xxpk_att_status_authorized": "已授权", "xxpk_att_status_unknown": "未知状态(%ld)", "xxpk_att_status_ios_not_support": "iOS版本不支持", "xxpk_app_state_active": "前台活跃", "xxpk_app_state_inactive": "前台非活跃", "xxpk_app_state_background": "后台", "xxpk_app_state_unknown": "未知状态(%ld)", "xxpk_log_manager_vk": "[VK] %@", "xxpk_log_manager_applovin": "[AppLovin] %@", "xxpk_log_manager_poopo": "[Poopo] %@", "xxpk_log_manager_appsflyer": "[<PERSON><PERSON><PERSON><PERSON><PERSON>] %@", "xxpk_log_manager_facebook": "[Facebook] %@", "xxpk_log_manager_firebase": "[Firebase] %@", "xxpk_log_manager_adjust": "[Adjust] %@", "xxpk_manager_status_exist": "✅存在", "xxpk_manager_status_not_exist": "❌不存在", "xxpk_manager_status_exist_version": "✅存在 版本:%@", "xxpk_log_manager_bdasignal": "[BDASignal] %@", "xxpk_log_manager_shanyan": "[<PERSON><PERSON><PERSON>] %@", "xxpk_log_manager_weichatopensdk": "[WechatOpenSDK] %@", "xxpk_log_manager_sigmob": "[Sigmob] %@", "xxpk_log_perform_selector": "[%@ 调用 %@]", "xxpk_log_perform_instance_not_found": "--- 实例方法签名未找到，尝试类方法 ---", "xxpk_log_perform_class_not_found": "--- 类方法签名也未找到，返回nil ---", "xxpk_log_perform_param_mismatch": "--- 参数数量不匹配: 期望 %lu，实际 %lu ---", "xxpk_log_iap_restore": "[IAP] 恢复订单 %lu/%lu 状态:%ld 详情:%@", "xxpk_log_iap_product_feedback": "[IAP] -----------收到产品反馈信息--------------", "xxpk_log_iap_product_count": "[IAP] 产品付费数量: %d", "xxpk_log_iap_product_title": "[IAP] 产品标题 %@", "xxpk_log_iap_product_desc": "[IAP] 产品描述信息: %@", "xxpk_log_iap_product_price": "[IAP] 价格: %@", "xxpk_log_iap_product_id": "[IAP] 产品ID: %@", "xxpk_log_iap_currency_info": "[IAP] 货币代码:%@  符号:%@", "xxpk_log_iap_start_purchase": "[IAP] 开始进行购买: %@,%@", "xxpk_log_iap_transaction_deferred": "[IAP] 交易延迟", "xxpk_log_iap_lost_transaction_id": "[IAP] 丢失交易标识符!!!", "xxpk_log_iap_purchase_complete": "[IAP] 购买完成,向自己的服务器验证 ---- %@,%@,paying:%lu", "xxpk_log_iap_add_products": "[IAP] 添加产品列表,%@,username:%@", "xxpk_log_iap_order_missing": "[IAP] 订单丢失!?", "xxpk_log_iap_transaction_failed": "[IAP] 交易失败,%@,order:%@,error:%@", "xxpk_log_iap_restore_received": "[IAP] 收到恢复交易: %lu", "xxpk_log_iap_restore_product_id": "[IAP] 恢复产品ID %@", "xxpk_log_iap_restore_error": "[IAP] 恢复错误%@", "xxpk_log_iap_order_verify_success": "[IAP] 订单在后台验证成功, 但是从 IAP 的未完成订单里取不到这比交易的错误 transactionIdentifier: %@", "xxpk_log_iap_prepare_delete_order": "[IAP] 准备删除储存订单:%@", "xxpk_log_iap_verify_callback": "[IAP] 验证回调:product:%@", "xxpk_log_iap_receipt_refresh_success": "[IAP] 票据刷新成功", "xxpk_log_iap_receipt_refresh_error": "[IAP] 票据刷新错误:%@", "xxpk_log_iap_verifying": "[IAP] 正在验证中....%@", "xxpk_log_iap_start_verify": "[IAP] 开始验证....%@", "xxpk_log_iap_error": "[IAP] %@", "xxpk_log_iap_delete_order_success": "[IAP] 完成删除订单:%@", "xxpk_log_iap_delete_order_failed": "[IAP] 删除订单失败:%@", "xxpk_log_mqtt_received": "[MQTT]收到消息主题: %@ \nTYPE: %@ \nJSON: %@", "xxpk_net_oauth": "o<PERSON>h", "xxpk_net_src": "src", "xxpk_net_auth_token": "auth_token", "xxpk_net_nonce": "nonce", "xxpk_net_src_facebook": "facebook", "xxpk_net_src_vk": "vk", "xxpk_net_src_poopo": "poopo", "xxpk_net_real_id": "real_id", "xxpk_net_real_name": "real_name", "xxpk_net_real_adjid": "adjid", "xxpk_net_code": "code", "xxpk_net_appid": "appid", "xxpk_net_source_language": "source_language", "xxpk_net_target_language": "target_language", "xxpk_net_text_list": "text_list", "xxpk_middleware_facebook": "XXGFacebookMiddleware", "xxpk_middleware_appflyer": "XXGAppFlyerMiddleware", "xxpk_middleware_firebase": "XXGFirebaseMiddleware", "xxpk_middleware_vk": "XXGVKMiddleware", "xxpk_middleware_adjust": "XXGAdjustMiddleware", "xxpk_middleware_poopo": "XXGPoopoMiddleware", "xxpk_middleware_applovin": "XXGAppLovinMiddleware", "xxpk_middleware_shanyan": "XXGShanYanMiddleware", "xxpk_middleware_bdasignal": "XXGBDASignalMiddleware", "xxpk_middleware_wechatopensdk": "XXGWechatOpenSDKMiddleware", "xxpk_middleware_sigmob": "XXGSigmobMiddleware", "xxpk_mqtt_exit": "exit", "xxpk_mqtt_unsubscribe": "unsubscribe", "xxpk_mqtt_topic": "topic", "xxpk_mqtt_qos": "qos", "xxpk_mqtt_type": "type", "xxpk_mqtt_type_redot": "red_dot", "xxpk_mqtt_type_marquee": "marquee", "xxpk_mqtt_type_alert": "alert", "xxpk_mqtt_type_popup": "popup", "xxpk_mqtt_type_ucenter": "ucenter", "xxpk_mqtt_type_offline": "offline", "xxpk_mqtt_type_apple_review": "apple_review", "xxpk_mqtt_label": "label", "xxpk_mqtt_click": "click", "xxpk_mqtt_jump": "jump", "xxpk_mqtt_url": "url", "xxpk_mqtt_action": "action", "xxpk_mqtt_open": "open", "xxpk_core_mt_openURL": "openURL", "xxpk_core_mt_changep": "changePassword", "xxpk_core_mt_bindm": "bindMobile", "xxpk_core_mt_switcha": "switchAccount", "xxpk_core_mt_ucenter": "ucenter", "xxpk_core_mt_iapRepair": "iapRepair", "xxpk_core_mt_getInfomation": "getInfomation", "xxpk_core_mt_getInfomation_uid": "uid", "xxpk_core_mt_getInfomation_name": "name", "xxpk_core_mt_getInfomation_token": "token", "xxpk_core_mt_getInfomation_fbuid": "fbuid", "xxpk_core_mt_getInfomation_fbtoken": "fbtoken", "xxpk_core_mt_getInfomation_fbauthtoken": "fbauthtoken", "xxpk_core_mt_getInfomation_fbnonce": "fbnonce", "xxpk_core_mt_getInfomation_user": "user", "xxpk_core_mt_userInfoSub": "userInfoSub", "xxpk_core_mt_closeSplash": "closeSplash", "xxpk_core_mt_openUserCenterSidebar": "openUserCenterSidebar", "xxpk_core_mt_accountRemove": "accountRemove", "xxpk_core_mt_getApiUrl": "getApiUrl", "xxpk_core_mt_getToken": "getToken", "xxpk_core_mt_facebookShare": "facebookShare", "xxpk_core_mt_facebookSub": "facebookSub", "xxpk_core_mt_facebookBind": "facebookBind", "xxpk_core_mt_facebookInvite": "facebookInvite", "xxpk_core_mt_popup": "popup", "xxpk_core_mt_coin_p": "coinPay", "xxpk_core_mt_continueOrder": "continueOrder", "xxpk_core_mt_wxbind": "wxL<PERSON>in", "xxpk_core_mt_wx_confirm_pay": "wxConfirmPay", "xxpk_core_mt_capture_screen": "captureScreen", "xxpk_core_mt_func_getInfomation": "returnInfomation(`%@`)", "xxpk_core_mt_func_getToken": "returnToken(`%@`)", "xxpk_core_mt_func_getApiUrl": "returnApiUrl(`%@`)", "xxpk_core_mt_func_captureScreenCallback": "captureScreenCallback(`%@`)", "xxpk_vk_roter_os": "%@%@landscape=%@&language=%@&token=%@", "xxpk_vk_add_token": "&token=", "xxpk_vk_roter_cn": "%@%@landscape=%@&language=%@%@", "xxpk_vk_and": "&", "xxpk_vk_wenhao": "?", "xxpk_tools_iap_priceString": "priceString", "xxpk_tools_iap_seriverOrder": "seriverOrder", "xxpk_tools_iap_userId": "userId", "xxpk_tools_iap_codeString": "codeString", "xxpk_tools_iap_productIdentifier": "productIdentifier", "xxpk_tools_iap_applicationUsername": "applicationUsername", "xxpk_tools_iap_transactionStatus": "transactionStatus", "xxpk_tools_iap_transactionDate": "transactionDate", "xxpk_tools_iap_domain": "domain.iap.error", "xxpk_tools_photolibaddusgaedes": "NSPhotoLibraryAddUsageDescription", "xxpk_tools_support_anguage": "en,zh-<PERSON>,<PERSON><PERSON>,th,vi,ru,ja,ko,es,pt,fr,de,ar", "xxpk_tools_countries": "xxpk_a_countries", "xxpk_tools_languages": "xxpk_a_languages", "xxpk_tools_logger_queue_label": "logger-queue-%@", "xxpk_tools_logger_level_verbose": "VERBOSE", "xxpk_tools_logger_level_debug": "DEBUG", "xxpk_tools_logger_level_info": "INFO", "xxpk_tools_logger_level_warning": "WARNING", "xxpk_tools_logger_level_error": "ERROR", "xxpk_tools_logger_level_all": "ALL", "xxpk_tools_logger_color_verbose": "🟣", "xxpk_tools_logger_color_debug": "🟢", "xxpk_tools_logger_color_info": "🔵", "xxpk_tools_logger_color_warning": "🟡", "xxpk_tools_logger_color_error": "🔴", "xxpk_tools_logger_color_all": "⚫️", "xxpk_tools_logger_formatter": "HH:mm:ss.SSS", "xxpk_tools_logger_format": "%@ [闲闲SDK-%@] %@", "xxpk_tools_logger_format_file": "%@[闲闲SDK-%@%@:%@] %@", "xxpk_tools_logger_null": "(null)", "xxpk_tools_logger_empty": "(无参数)", "xxpk_tools_logger_text": "(文本数据)", "xxpk_tools_logger_text_chars": "(文本数据: %lu chars)", "xxpk_tools_logger_data": "(二进制数据: %lu bytes)", "xxpk_tools_logger_no_error": "(无错误)", "xxpk_tools_logger_code": "错误码:", "xxpk_tools_logger_desc": "描述:", "xxpk_tools_logger_info": "详细信息:", "xxpk_tools_logger_items": "...%ld items...", "xxpk_tools_logger_data_bytes": "<Data: %lu bytes>", "xxpk_tools_logger_date_format": "yyyy-MM-dd HH:mm:ss", "xxpk_tools_logger_file_date_format": "yyyy-MM-dd", "xxpk_tools_logger_uptime_format": "%0.2d:%0.2d:%0.2d.%03d", "xxpk_tools_logger_file_separator": "=== %@ ===\n", "xxpk_tools_logger_request_format": "🌐 请求: %@\n参数: %@", "xxpk_tools_logger_response_format": "📥 响应: %@\n数据: %@", "xxpk_tools_logger_network_error_format": "❌ 网络错误: %@\n错误: %@", "xxpk_tools_logger_ellipsis": "...", "xxpk_tools_logger_ui_title": "日志查看器", "xxpk_tools_logger_ui_not_init": "日志系统未初始化\n请先调用 [ZBLogViewController setupLogger]", "xxpk_tools_logger_ui_no_logs": "暂无日志记录", "xxpk_tools_logger_ui_info": "提示", "xxpk_tools_logger_ui_no_files": "暂无日志文件", "xxpk_tools_logger_ui_ok": "确定", "xxpk_tools_logger_ui_share_logs": "分享日志", "xxpk_tools_logger_ui_share_all": "分享所有日志", "xxpk_tools_logger_ui_share_file": "分享 %@", "xxpk_tools_logger_ui_cancel": "取消", "xxpk_tools_logger_ui_select_date": "选择日期", "xxpk_tools_logger_ui_all_logs": "所有日志", "xxpk_tools_logger_ui_today": "今天", "xxpk_tools_logger_ui_yesterday": "昨天", "xxpk_tools_logger_ui_date_format": "yyyy年MM月dd日", "xxpk_tools_country_model": {"xxpk_name": "name", "xxpk_countryCode": "code", "xxpk_dialCode": "dial_code"}, "xxpk_ui_base_btn_back": "←", "xxpk_ui_base_btn_close": "✕", "xxpk_ui_base_keyboardShimWin": "_UIAlertControllerShimPresenterWindow", "xxpk_ui_FLOAT_CENTER": "FLOAT_CENTER", "xxpk_ui_action": "action", "xxpk_ui_show": "show", "xxpk_ui_hide": "hide", "xxpk_ui_show_once": "show_once", "xxpk_ui_url": "url", "xxpk_ui_boxName": "username", "xxpk_ui_boxKey": "password", "xxpk_ui_appinfo_copytip": "已复制到剪贴板", "xxpk_ui_appinfo_info": "工程配置", "xxpk_ui_appinfo_server": "产品信息", "xxpk_ui_appinfo_body": "SDK信息", "xxpk_ui_appinfo_device": "设备信息", "xxpk_ui_appinfo_adaption": "SDK配置", "xxpk_ui_appinfo_extra": "三方参数", "xxpk_ui_service_sysicon_header": "person.fill.questionmark", "xxpk_ui_service_sysicon_qq": "message.fill", "xxpk_ui_service_sysicon_tel": "phone.fill", "xxpk_ui_service_sysicon_url": "bubble.right.fill", "xxpk_ui_service_open_qq": "mqq://im/chat?chat_type=wpa&uin=%@&version=1&src_type=web", "xxpk_ui_service_open_tel": "tel://%@", "xxpk_ui_service_version": "SDK VERSION：%@", "xxpk_guest": "guest", "xxpk_register": "register", "xxpk_vx": "weixin", "xxpk_one_click": "one_click", "xxpk_vk": "vk", "xxpk_facebook": "facebook", "xxpk_poopo": "poopo", "xxpk_code_comein": "login", "xxpk_code_bind": "bind", "xxpk_code_forget": "password", "xxpk_code_jia": "+", "xxpk_wk_abonementjs": "var meta = document.createElement('meta'); meta.setAttribute('name', 'viewport'); meta.setAttribute('content', 'width=device-width'); document.getElementsByTagName('head')[0].appendChild(meta);", "xxpk_wk_injectionJSString": "var script = document.createElement('meta'); script.name = 'viewport'; script.content=\"width=device-width, user-scalable=no\"; document.getElementsByTagName('head')[0].appendChild(script);", "xxpk_wk_touchCallout": "document.documentElement.style.webkitTouchCallout='none';", "xxpk_wk_UserSelect": "document.documentElement.style.webkitUserSelect='none';", "xxpk_wk_http": "http", "xxpk_wk_kds_token": "kds_token", "xxpk_p_selcitem_note_b": "<b>", "xxpk_p_selcitem_note_bb": "</b>", "xxpk_p_block_orderExtra": "__OrderExtraBlock", "xxpk_content_txt": "txt", "xxpk_popup_style": "style", "xxpk_popup_width": "width", "xxpk_popup_height": "height", "xxpk_popup_url": "url", "xxpk_popup_close_button": "close_button", "xxpk_popup_shade_close": "shade_close", "xxpk_popup_is_alpha": "is_alpha", "xxpk_func_guestBtnDidClick": "xxpk_guestBtnDidClick:", "xxpk_func_mobileBtnDidClick": "xxpk_mobileBtnDidClick:", "xxpk_func_registerBtnDidClick": "xxpk_registerBtnDidClick:", "xxpk_func_vxBtnDidClick": "xxpk_vxBtnDidClick:", "xxpk_func_oneClickBtnDidClick": "xxpk_oneClickBtnDidClick:", "xxpk_func_vkBtnDidClick": "xxpk_vkBtnDidClick:", "xxpk_func_facebookBtnDidClick": "xxpk_facebookBtnDidClick:", "xxpk_func_poopoBtnDidClick": "xxpk_poopoBtnDidClick:", "xxpk_img_comein_guest": "xxpk_comein_guest", "xxpk_img_comein_mobile": "xxpk_comein_mobile", "xxpk_img_comein_register": "xxpk_comein_register", "xxpk_img_comein_vx": "xxpk_comein_vx", "xxpk_img_comein_oneclick": "xxpk_comein_oneclick", "xxpk_img_comein_vk": "xxpk_comein_vk", "xxpk_img_comein_facebook": "xxpk_comein_facebook", "xxpk_img_ps_look": "xxpk_ps_look", "xxpk_img_ps_unlook": "xxpk_ps_unlook", "xxpk_img_check_box_on": "xxpk_check_box_on", "xxpk_img_check_box_off": "xxpk_check_box_off", "xxpk_img_float_ball": "xxpk_float_ball", "xxpk_img_sp_cell_ns": "xxpk_sp_cell_ns", "xxpk_img_sp_cell_ss": "xxpk_sp_cell_ss", "xxpk_img_code_pulldown": "xxpk_code_pulldown", "xxpk_img_onclick_back": "xxpk_onclick_back", "xxpk_img_ucenter_colse": "xxpk_ucenter_colse", "xxpk_textColor": "424242", "xxpk_mainColor": "1E9FFF", "xxpk_backgroundColor": "FAFAFA", "xxpk_F5F5F5": "F5F5F5", "xxpk_E1F5FE": "E1F5FE", "xxpk_333333": "333333", "xxpk_666666": "666666", "xxpk_contentSizeWidth": 350, "xxpk_contentSizeHeight": 238, "xxpk_ppselecteWidth": 400, "xxpk_ucenterW": 420, "xxpk_float045": 0.45, "xxpk_float09": 0.9, "xxpk_float1": 1, "xxpk_float2": 2, "xxpk_float3": 3, "xxpk_float4": 4, "xxpk_float5": 5, "xxpk_float6": 6, "xxpk_float7": 7, "xxpk_float8": 8, "xxpk_float9": 9, "xxpk_float10": 10, "xxpk_float12": 12, "xxpk_float13": 13, "xxpk_float14": 14, "xxpk_float15": 15, "xxpk_float16": 16, "xxpk_float17": 17, "xxpk_float18": 18, "xxpk_float20": 20, "xxpk_float22": 22, "xxpk_float24": 24, "xxpk_float25": 25, "xxpk_float26": 26, "xxpk_float28": 28, "xxpk_float30": 30, "xxpk_float35": 35, "xxpk_float36": 36, "xxpk_float38": 38, "xxpk_float40": 40, "xxpk_float43": 43, "xxpk_float45": 45, "xxpk_float48": 48, "xxpk_float52": 52, "xxpk_float55": 55, "xxpk_float57": 57, "xxpk_float60": 60, "xxpk_float70": 70, "xxpk_float75": 75, "xxpk_float120": 120, "xxpk_float180": 180, "xxpk_start_body": {"xxpk_deviceId": "device.id", "xxpk_appId": "app.id", "xxpk_appBundleId": "app.bundle_id", "xxpk_appVersion": "app.version", "xxpk_appName": "app.name", "xxpk_sdkName": "sdk.name", "xxpk_version": "sdk.version", "xxpk_campaign": "sdk.campaign", "xxpk_platform": "sdk.platform", "xxpk_type": "sdk.type"}, "xxpk_device_info": {"xxpk_name": "name", "xxpk_idfa": "idfa", "xxpk_idfv": "idfv", "xxpk_model": "model", "xxpk_os": "os", "xxpk_osVersion": "osv", "xxpk_vindatool": "jailbreak", "xxpk_docPath": "doc_uuid", "xxpk_network": "network", "xxpk_operator": "operator", "xxpk_lang": "lang", "xxpk_scale": "scale", "xxpk_screen": "screen", "xxpk_landscape": "landscape", "xxpk_afid": "afid", "xxpk_firebaseId": "app_instance_id", "xxpk_uniqueId": "uuid"}, "xxpk_product_body": {"xxpk_cpOrderId": "order.cp_order_id", "xxpk_productCode": "order.item_id", "xxpk_productName": "order.item", "xxpk_amount": "order.amount", "xxpk_serverId": "order.server", "xxpk_roleName": "order.role_name", "xxpk_roleId": "order.role_id", "xxpk_roleLevel": "order.role_level", "xxpk_extraInfo": "order.cp_extra_info", "xxpk_orderId": "order.id", "xxpk_currency": "order.currency"}, "xxpk_receipt_body": {"xxpk_order_id": "order.id", "xxpk_receipt_data": "apple_iap.receipt_data", "xxpk_product_code": "apple_iap.item_id", "xxpk_transaction_id": "apple_iap.transaction_id", "xxpk_currency": "apple_iap.currency", "xxpk_price": "apple_iap.price"}, "xxpk_role_body": {"xxpk_serverName": "role.server_name", "xxpk_serverId": "role.server", "xxpk_roleId": "role.id", "xxpk_roleLevel": "role.level", "xxpk_roleName": "role.name", "xxpk_extend": "role.extend"}, "xxpk_action_item": {"xxpk_type": "type", "xxpk_target": "target", "xxpk_message": "message", "xxpk_force": "force", "xxpk_bangs": "bangs", "xxpk_orientation": "orientation"}, "xxpk_box_content": {"xxpk_boxId": "id", "xxpk_boxName": "name", "xxpk_boxKey": "password", "xxpk_boxToken": "token", "xxpk_boxMobile": "mobile", "xxpk_lastComeinTime": "time", "xxpk_boxType": "type", "xxpk_created": "created", "xxpk_fbBind": "fb_bind", "xxpk_mobileBind": "mobile_bind", "xxpk_facebookUid": "fb_uid", "xxpk_facebookToken": "fb_token", "xxpk_facebookAuthToken": "fb_auth_token", "xxpk_facebookNonce": "fb_nonce", "xxpk_vkBind": "vk_bind", "xxpk_vkUid": "vk_uid", "xxpk_vkToken": "vk_token", "xxpk_poopoUid": "poopo_uid", "xxpk_poopoToken": "poopo_token"}, "xxpk_skin_model": {"xxpk_status": "status", "xxpk_name": "name", "xxpk_image": "image", "xxpk_labelColor": "label.color", "xxpk_labelText": "label.text"}, "xxpk_theme_color": {"xxpk_backgroundColor": "background.color", "xxpk_mainColor": "color", "xxpk_textColor": "font.color"}, "xxpk_docker_cof": {"xxpk_bangs": "bangs", "xxpk_image": "image", "xxpk_dot_image": "red_dot.image", "xxpk_dot_ofx": "red_dot.offset.x", "xxpk_dot_ofy": "red_dot.offset.y", "xxpk_status": "status"}, "xxpk_service_info": {"xxpk_agreement": "agreement", "xxpk_fb_home": "fb_home", "xxpk_vk_home": "vk_home", "xxpk_one_click_agreement": "one_click_agreement", "xxpk_privacy": "privacy", "xxpk_qq": "qq", "xxpk_tel": "tel", "xxpk_url": "url"}, "xxpk_box_center_cof": {"xxpk_bangs_color": "bangs_color", "xxpk_size": "size", "xxpk_url": "url", "xxpk_add_token": "add_token"}, "xxpk_adaption_cof": {"xxpk_adaption_type": "adaption.type", "xxpk_autoComein": "auto_login", "xxpk_logStatus": "log_status", "xxpk_realname_bg": "realname_bg", "xxpk_adaption_report_adjust": "adaption.report.adjust", "xxpk_adaption_report_appsFlyer": "adaption.report.apps_flyer", "xxpk_adaption_report_facebook": "adaption.report.facebook", "xxpk_adaption_report_firebase": "adaption.report.firebase", "xxpk_adaption_skin_btns": "adaption.skin.login_btns", "xxpk_adaption_skin_comein": "adaption.skin.login", "xxpk_adaption_skin_comein_only": "adaption.skin.login.register.only_login", "xxpk_adaption_skin_logo": "adaption.skin.logo", "xxpk_adaption_skin_theme": "adaption.skin.theme", "xxpk_docker": "docker", "xxpk_service": "service", "xxpk_box_center": "user_center"}, "xxpk_extra_params": {"xxpk_adjustRegister": "adjustRegister", "xxpk_adjustLogin": "adjustLogin", "xxpk_adjustClickPay": "adjustClickPay", "xxpk_adjustPay": "adjustPay", "xxpk_adjustAppToken": "adjustAppToken", "xxpk_adjustActivate": "adjustActivate", "xxpk_afDevKey": "afDevKey", "xxpk_afAppid": "appid", "xxpk_afClickPay": "afClickPay", "xxpk_afActivate": "afActivate", "xxpk_afPay": "afPay", "xxpk_fireClickPay": "fireClickPay", "xxpk_fireActivate": "fireActivate", "xxpk_firePay": "firePay", "xxpk_fbClickPay": "fbClickPay", "xxpk_fbPay": "fbPay", "xxpk_vk_clientid": "vk_id", "xxpk_vk_client_secret": "vk_secret", "xxpk_max_key": "adv.max_key", "xxpk_max_reward_id": "adv.max_reward_id", "xxpk_poopo_code": "poopo_code"}, "xxpk_extends": {"xxpk_shanya_secret": "shanyan.one_click_secret", "xxpk_sigmob_appid": "shanyan.sigmob_appid", "xxpk_sigmob_key": "shanyan.sigmob_key", "xxpk_sigmob_placementId": "shanyan.sigmob_placementId"}, "xxpk_select_product": {"xxpk_name": "name", "xxpk_type": "type", "xxpk_logo": "logo", "xxpk_order_url": "order_url", "xxpk_note": "note"}, "xxpk_product": {"xxpk_currency": "currency", "xxpk_orderId": "id", "xxpk_pay_method": "pay_method", "xxpk_price": "price", "xxpk_amount": "amount", "xxpk_amount_text": "amount_text", "xxpk_discount_text": "discount_text", "xxpk_display_view": "display_view"}, "xxpk_mqtt_info": {"xxpk_ip": "ip", "xxpk_port": "port", "xxpk_username": "username", "xxpk_password": "password", "xxpk_client_id": "client_id", "xxpk_keep_alive": "keep_alive", "xxpk_topics": "topics"}, "xxpk_mqtt_topic_info": {"xxpk_type": "type", "xxpk_message": "message", "xxpk_count": "count", "xxpk_position": "position", "xxpk_speed": "speed", "xxpk_style_background_alpha": "style.background.alpha", "xxpk_style_background_color": "style.background.color", "xxpk_style_text_color": "style.text.color", "xxpk_style_text_font_size": "style.text.font.size", "xxpk_click_action": "click.action", "xxpk_click_url": "click.url", "xxpk_alert_buttons": "buttons", "xxpk_title": "title", "xxpk_action": "action", "xxpk_url": "url", "xxpk_retry": "retry"}, "xxpk_server_info": {"xxpk_product_id": "product_id", "xxpk_run_env": "run_env", "xxpk_timestamp": "timestamp", "xxpk_version": "version"}, "xxpk_net_list": {"xxpk_list_account_remove": "account_remove", "xxpk_list_adjustid_report": "adjustid_report", "xxpk_list_adview": "adview", "xxpk_list_asa_report": "asa_report", "xxpk_list_facebook_auth": "facebook_auth", "xxpk_list_id_report": "id_report", "xxpk_list_comein": "login", "xxpk_list_comein_guest": "login_guest", "xxpk_list_comein_mobile": "login_mobile", "xxpk_list_comein_token": "login_token", "xxpk_list_comein_v": "login_weixin", "xxpk_list_comin_one_click": "login_one_click", "xxpk_list_bind_mobile": "mobile", "xxpk_list_booking": "order", "xxpk_list_coin_booking": "coin_order", "xxpk_list_booking_check": "order_check", "xxpk_list_coin_booking_check": "coin_order_check", "xxpk_list_booking_extra": "order_extra", "xxpk_list_booking_receipt": "order_receipt", "xxpk_list_password_change": "password_change", "xxpk_list_password_reset": "password_reset", "xxpk_list_real_name": "real_name", "xxpk_list_register": "register", "xxpk_list_role": "role", "xxpk_list_sms_code": "sms_code", "xxpk_list_subscribe": "subscribe", "xxpk_list_vk_auth": "vk_auth", "xxpk_list_v_auth": "weixin_auth", "xxpk_list_test_report": "test_report", "xxpk_list_translate": "translate"}, "xxpk_security_jb_paths": ["/Applications/Cydia.app", "/usr/sbin/sshd", "/bin/bash", "/etc/apt", "/Library/MobileSubstrate", "/User/Applications/"], "xxpk_security_dylib_set": ["/usr/lib/CepheiUI.framework/CepheiUI", "/usr/lib/libsubstitute.dylib", "/usr/lib/substitute-inserter.dylib", "/usr/lib/substitute-loader.dylib", "/usr/lib/substrate/SubstrateLoader.dylib", "/usr/lib/substrate/SubstrateInserter.dylib", "/Library/MobileSubstrate/MobileSubstrate.dylib", "/Library/MobileSubstrate/DynamicLibraries/0Shadow.dylib"], "xxpk_security_check_paths": ["/Application/Cydia.app", "/Library/MobileSubstrate/MobileSubstrate.dylib", "/bin/bash", "/usr/sbin/sshd", "/etc/apt", "/usr/bin/ssh", "/private/var/lib/apt", "/private/var/lib/cydia", "/private/var/tmp/cydia.log", "/Applications/WinterBoard.app", "/var/lib/cydia", "/private/etc/dpkg/origins/debian", "/bin.sh", "/private/etc/apt", "/etc/ssh/sshd_config", "/private/etc/ssh/sshd_config", "/Applications/SBSetttings.app", "/private/var/mobileLibrary/SBSettingsThemes/", "/private/var/stash", "/usr/libexec/sftp-server", "/usr/libexec/cydia/", "/usr/sbin/frida-server", "/usr/bin/cycript", "/usr/local/bin/cycript", "/usr/lib/libcycript.dylib", "/System/Library/LaunchDaemons/com.saurik.Cydia.Startup.plist", "/System/Library/LaunchDaemons/com.ikey.bbot.plist", "/Applications/FakeCarrier.app", "/Library/MobileSubstrate/DynamicLibraries/Veency.plist", "/Library/MobileSubstrate/DynamicLibraries/LiveClock.plist", "/usr/libexec/ssh-keysign", "/usr/libexec/sftp-server", "/Applications/blackra1n.app", "/Applications/IntelliScreen.app", "/Applications/Snoop-itConfig.app", "/var/lib/dpkg/info"], "xxpk_security_check_classes": ["HBPreferences"], "xxpk_security_cydia_url_1": "cydia://package/com.avl.com", "xxpk_security_cydia_url_2": "cydia://package/com.example.package", "xxpk_security_test_path": "/private/avl.txt", "xxpk_security_test_content": "AVL was here", "xxpk_security_system_lib_path": "/usr/lib/system/libsystem_kernel.dylib", "xxpk_security_dyld_env_var": "DYLD_INSERT_LIBRARIES", "xxpk_security_symlink_paths": ["/Applications", "/var/stash/Library/Ringtones", "/var/stash/Library/Wallpaper", "/var/stash/usr/include", "/var/stash/usr/libexec", "/var/stash/usr/share", "/var/stash/usr/arm-apple-darwin9"]}