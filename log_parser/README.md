# XXGPlayKit 日志解析工具

这是一个跨平台的日志解析工具，用于解析 XXGPlayKit SDK 导出的日志文件。支持 Mac 和 Windows 系统。

## 功能特性

- ✅ 支持加密和明文日志解析
- ✅ 图形界面和命令行两种使用方式
- ✅ 跨平台支持（Mac/Windows/Linux）
- ✅ 日志级别识别和格式化
- ✅ 按日期文件分组显示
- ✅ 解析结果导出功能

## 系统要求

- Python 3.6 或更高版本
- tkinter（通常随 Python 一起安装）

## 安装和使用

### 1. 检查 Python 环境

```bash
python3 --version
# 或者
python --version
```

### 2. 运行工具

#### 图形界面模式（推荐）

```bash
# Mac/Linux
python3 log_parser.py --gui

# Windows
python log_parser.py --gui
```

#### 命令行模式

```bash
# 解析日志并在控制台显示
python3 log_parser.py -f /path/to/logfile.txt

# 解析日志并保存到文件
python3 log_parser.py -f /path/to/logfile.txt -o /path/to/output.txt
```

## 使用说明

### 图形界面使用

1. 运行工具后会打开图形界面
2. 点击"浏览"按钮选择要解析的日志文件
3. 点击"解析日志"按钮开始解析
4. 解析结果会显示在下方的文本框中
5. 可以点击"导出解析结果"保存解析后的内容

### 支持的日志格式

#### 明文日志格式
```
=== 2025-01-20 ===
🔵[闲闲SDK-14:30:25.123functionName:42] 这是一条信息日志
🟡[闲闲SDK-14:30:26.456anotherFunction:58] 这是一条警告日志
```

#### 加密日志格式
```
=== 2025-01-20 ===
SGVsbG8gV29ybGQgU0RLIEluaXRpYWxpemVk
TG9hZGluZyBDb25maWd1cmF0aW9uIEZpbGU=
TG9nZ2VyIFN5c3RlbSBJbml0aWFsaXplZA==
```

**解析结果示例：**
```
=== 解密日志结果 ===

=== 2025-01-20 ===

Hello World SDK Initialized
Loading Configuration File
Logger System Initialized
```

### 解析说明

- **加密日志**: 自动解密base64编码的内容，显示原始日志信息
- **明文日志**: 保持原始格式，包括颜色图标、时间戳、函数名等信息
- **文件分隔**: 按日期文件自动分组显示

## 命令行参数

```
usage: log_parser.py [-h] [-f FILE] [-o OUTPUT] [--gui]

XXGPlayKit 日志解析工具

optional arguments:
  -h, --help            显示帮助信息
  -f FILE, --file FILE  要解析的日志文件路径
  -o OUTPUT, --output OUTPUT
                        输出文件路径
  --gui                 启动图形界面
```

## 示例

### 示例1：使用图形界面
```bash
python3 log_parser.py --gui
```

### 示例2：命令行解析并显示
```bash
python3 log_parser.py -f logs/2025-01-20.txt
```

### 示例3：命令行解析并保存
```bash
python3 log_parser.py -f logs/2025-01-20.txt -o parsed_logs.txt
```

## 故障排除

### 1. Python 未安装
- Mac: 使用 Homebrew 安装 `brew install python3`
- Windows: 从 [python.org](https://python.org) 下载安装

### 2. tkinter 模块缺失
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo yum install tkinter
```

### 3. 编码问题
确保日志文件使用 UTF-8 编码保存。

### 4. Unicode字符错误
如果遇到类似 "character U+xxxxx is above the range allowed by Tcl" 的错误：
- 工具会自动清理超出范围的Unicode字符
- 超出范围的字符会被替换为 "�" 符号
- 这不影响日志内容的正常解析

## 开发说明

### 项目结构
```
log_parser/
├── log_parser.py           # 主程序
├── README.md              # 说明文档
├── requirements.txt       # 依赖列表
├── run_parser.bat         # Windows启动脚本
├── run_parser.sh          # Mac/Linux启动脚本
└── sample_logs/           # 示例日志文件
    ├── sample_plain.txt   # 明文日志示例
    └── 2568-05-30.txt     # 加密日志示例
```

### 扩展功能

如需添加新的解析功能，可以修改 `LogParser` 类中的相应方法：

- `parse_log_content()`: 主解析逻辑
- `parse_encrypted_log()`: 加密日志解析（自动解密base64内容）
- `parse_plain_log()`: 明文日志解析（保持原始格式）
- `detect_encryption()`: 自动检测日志是否加密

## 注意事项

1. **加密日志处理**:
   - 工具会自动检测日志是否加密（基于base64格式识别）
   - 加密日志自动解密并显示原始内容
   - 解密失败时显示原始base64内容

2. **革命性Unicode处理**:
   - **GUI显示**：emoji → [U+码点] (如：🔵 → [U+1F535])
   - **导出文件**：[U+码点] → emoji (完美恢复原始字符)
   - **命令行**：保留原始emoji字符
   - **优势**：GUI稳定无错误 + 导出文件完整保真

3. **文件大小**: 对于特别大的日志文件，建议使用命令行模式以获得更好的性能

4. **字符编码**: 确保日志文件使用 UTF-8 编码

5. **日志格式**:
   - 明文日志：保持原始格式，包括所有颜色图标和时间信息
   - 加密日志：解密后显示原始日志内容

## 更新日志

- v1.5.0:
  - 🎯 革命性Unicode处理方案：保存码点，导出恢复
  - GUI显示：emoji → [U+码点] (完全Tcl兼容)
  - 导出文件：[U+码点] → emoji (完美恢复原始字符)
  - 新增 `restore_unicode_from_export()` 方法实现可逆转换
  - 用户体验最佳：GUI稳定 + 导出完整

- v1.4.1:
  - 彻底修复GUI模式下的Tcl Unicode错误
  - 添加双重Unicode安全处理：解析时+显示时
  - 新增 `make_text_tcl_safe()` 方法确保GUI文本完全兼容
  - 完全解决"character U+1f535 is above the range allowed by Tcl"错误

- v1.4.0:
  - 完全修复GUI模式下的Unicode字符处理问题
  - 图形界面模式：将emoji转换为文本标识（🔵→[蓝]，🟢→[绿]等）
  - 命令行模式：保留原始emoji字符显示
  - 解决了"character U+xxxxx is above the range allowed by Tcl"错误

- v1.3.0:
  - 完全修复加密日志解密功能，实现与OC项目一致的XOR解密算法
  - 支持随机IV的加密数据解密（前16字节为IV，后续为XOR加密数据）
  - 解密后的日志内容完全可读，包含完整的SDK运行信息

- v1.2.0:
  - 修复Unicode字符处理问题，支持包含特殊字符的日志文件
  - 自动清理超出Tcl/tkinter支持范围的Unicode字符
  - 改进错误处理和文件读取稳定性

- v1.1.0:
  - 优化加密日志解析，自动解密base64内容
  - 简化明文日志显示，保持原始格式
  - 移除复杂的日志级别解析
  - 改进加密检测算法

- v1.0.0: 初始版本，支持基本的日志解析功能
