#!/bin/bash

echo "XXGPlayKit 日志解析工具"
echo "========================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.6或更高版本"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "Mac用户可以使用: brew install python3"
    else
        echo "Linux用户可以使用包管理器安装python3"
    fi
    exit 1
fi

# 检查tkinter是否可用
python3 -c "import tkinter" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: tkinter模块不可用，可能需要安装"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "Ubuntu/Debian用户可以运行: sudo apt-get install python3-tk"
    fi
fi

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# 启动图形界面
echo "正在启动图形界面..."
cd "$SCRIPT_DIR"
python3 log_parser.py --gui
