# XXGPlayKit 日志解析工具 - Windows 使用指南

## 系统要求

- Windows 7/8/10/11
- Python 3.6 或更高版本
- 所需Python模块: tkinter (通常随Python一起安装)

## 安装

1. 从 https://python.org 下载Python
2. 安装时确保勾选 "Add Python to PATH"
3. 将日志解析工具文件解压到任意文件夹

## 使用方法

### 方法1: 双击运行GUI
- 双击 `run_parser.bat` 启动图形界面

### 方法2: 命令行
```cmd
# 解析日志文件并显示结果
python log_parser.py -f "path\to\logfile.txt"

# 解析并保存到输出文件
python log_parser.py -f "path\to\logfile.txt" -o "output.txt"

# 启动GUI
python log_parser.py --gui
```

## 功能特性

- **自动加密检测**: 自动检测日志文件是否加密
- **Base64解密**: 使用XOR算法解密base64编码的日志条目
- **Unicode支持**: 正确处理emoji和特殊字符
- **跨平台**: 支持Windows和Mac系统
- **导出功能**: 导出解析结果时恢复原始emoji字符

## 故障排除

### "Python is not recognized" 错误
- 重新安装Python并勾选 "Add Python to PATH"
- 或使用完整路径: `C:\Python39\python.exe log_parser.py`

### 编码问题
- 工具自动处理UTF-8编码
- GUI显示时，特殊字符会转换为安全格式
- 导出时恢复原始字符

### 语法错误
- 确保使用Python 3.6或更高版本
- 检查所有文件在同一目录下

## 文件结构
```
log_parser/
├── log_parser.py          # 主解析脚本
├── run_parser.bat         # Windows启动器
├── test_parser.py         # 测试脚本
└── README_Windows.md      # 本文件
```

## 技术支持

如果遇到问题，请检查：
1. Python版本: `python --version`
2. 文件编码为UTF-8
3. 日志文件格式受支持
