#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for log parser
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from log_parser import LogParser

def test_basic_functionality():
    """Test basic functionality"""
    parser = LogParser()
    
    # Test plain text log
    plain_content = """=== Test Log ===
2024-01-01 10:00:00 INFO Test message
2024-01-01 10:01:00 ERROR Error message
=== End ==="""
    
    print("Testing plain text log parsing...")
    result = parser.parse_log_content(plain_content)
    print("Result:")
    print(result)
    print("\n" + "="*50 + "\n")
    
    # Test Unicode content
    unicode_content = "Test with emoji: 🔵🟢🟡🔴"
    print("Testing Unicode content...")
    cleaned = parser.clean_unicode_content(unicode_content)
    print("Cleaned content:", cleaned)
    print("\n" + "="*50 + "\n")
    
    print("All tests completed successfully!")

if __name__ == "__main__":
    test_basic_functionality()
